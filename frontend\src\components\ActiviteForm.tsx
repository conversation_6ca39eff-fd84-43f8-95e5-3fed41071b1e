import React, { useState, useEffect } from 'react';
import { X, Save, Calendar, Clock, MapPin, DollarSign, Users, FileText } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import Select from './Select';

import { addActivite, updateActivite, getEnseignants } from '../services/api';
import { Activite, Enseignant } from '../types';

interface ActiviteFormProps {
  activite?: Activite | null;
  onClose: () => void;
  onSuccess: () => void;
}

const ActiviteForm: React.FC<ActiviteFormProps> = ({ activite, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [enseignants, setEnseignants] = useState<Enseignant[]>([]);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    nom_activite: '',
    description: '',
    type_activite: '',
    date_debut: '',
    date_fin: '',
    prix: '',
    id_responsable: '',
    jour_semaine: '',
    heure_debut: '',
    heure_fin: '',
    lieu: '',
    capacite_max: '',
    statut: 'active'
  });

  useEffect(() => {
    fetchEnseignants();
    if (activite) {
      setFormData({
        nom_activite: activite.nom_activite || '',
        description: activite.description || '',
        type_activite: activite.type_activite || '',
        date_debut: activite.date_debut || '',
        date_fin: activite.date_fin || '',
        prix: activite.prix?.toString() || '',
        id_responsable: activite.id_responsable?.toString() || '',
        jour_semaine: activite.jour_semaine || '',
        heure_debut: activite.heure_debut || '',
        heure_fin: activite.heure_fin || '',
        lieu: activite.lieu || '',
        capacite_max: activite.capacite_max?.toString() || '50',
        statut: activite.statut || 'active'
      });
    }
  }, [activite]);

  const fetchEnseignants = async () => {
    try {
      const response = await getEnseignants();
      setEnseignants(response.data.data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des enseignants:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.nom_activite.trim()) {
      setError('Le nom de l\'activité est obligatoire');
      return false;
    }
    if (!formData.type_activite) {
      setError('Le type d\'activité est obligatoire');
      return false;
    }
    if (!formData.prix || isNaN(Number(formData.prix)) || Number(formData.prix) < 0) {
      setError('Le prix doit être un nombre positif');
      return false;
    }
    if (formData.date_debut && formData.date_fin && formData.date_debut > formData.date_fin) {
      setError('La date de fin doit être postérieure à la date de début');
      return false;
    }
    if (formData.heure_debut && formData.heure_fin && formData.heure_debut >= formData.heure_fin) {
      setError('L\'heure de fin doit être postérieure à l\'heure de début');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const dataToSubmit = {
        ...formData,
        prix: Number(formData.prix),
        capacite_max: formData.capacite_max ? Number(formData.capacite_max) : 50,
        id_responsable: formData.id_responsable ? Number(formData.id_responsable) : null
      };

      if (activite) {
        await updateActivite(activite.id_activite, dataToSubmit);
        setSuccessMessage('Activité modifiée avec succès');
      } else {
        await addActivite(dataToSubmit);
        setSuccessMessage('Activité créée avec succès');
      }

      setTimeout(() => {
        onSuccess();
      }, 1500);

    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(error.response?.data?.message || 'Erreur lors de la sauvegarde de l\'activité');
    } finally {
      setLoading(false);
    }
  };

  const typeOptions = [
    { value: '', label: 'Sélectionner un type' },
    { value: 'sportive', label: 'Sportive' },
    { value: 'culturelle', label: 'Culturelle' },
    { value: 'autre', label: 'Autre' }
  ];

  const jourOptions = [
    { value: '', label: 'Sélectionner un jour' },
    { value: 'lundi', label: 'Lundi' },
    { value: 'mardi', label: 'Mardi' },
    { value: 'mercredi', label: 'Mercredi' },
    { value: 'jeudi', label: 'Jeudi' },
    { value: 'vendredi', label: 'Vendredi' },
    { value: 'samedi', label: 'Samedi' }
  ];

  const responsableOptions = [
    { value: '', label: 'Aucun responsable' },
    ...enseignants.map(enseignant => ({
      value: enseignant.id_enseignant.toString(),
      label: enseignant.user ? `${enseignant.user.prenom} ${enseignant.user.nom}` : `Enseignant ${enseignant.id_enseignant}`
    }))
  ];

  const statutOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'terminee', label: 'Terminée' }
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[95vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {activite ? 'Modifier l\'activité' : 'Nouvelle activité'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {successMessage}
          </div>
        )}

        {/* Informations de base */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <Input
              label="Nom de l'activité"
              name="nom_activite"
              value={formData.nom_activite}
              onChange={handleInputChange}
              required
              leftIcon={<FileText size={16} />}
            />
          </div>

          <Select
            label="Type d'activité"
            name="type_activite"
            value={formData.type_activite}
            onChange={handleInputChange}
            options={typeOptions}
            required
          />

          <Select
            label="Statut"
            name="statut"
            value={formData.statut}
            onChange={handleInputChange}
            options={statutOptions}
            required
          />
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Description de l'activité..."
          />
        </div>

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Date de début"
            name="date_debut"
            type="date"
            value={formData.date_debut}
            onChange={handleInputChange}
            leftIcon={<Calendar size={16} />}
          />

          <Input
            label="Date de fin"
            name="date_fin"
            type="date"
            value={formData.date_fin}
            onChange={handleInputChange}
            leftIcon={<Calendar size={16} />}
          />
        </div>

        {/* Planning */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Select
            label="Jour de la semaine"
            name="jour_semaine"
            value={formData.jour_semaine}
            onChange={handleInputChange}
            options={jourOptions}
          />

          <Input
            label="Heure de début"
            name="heure_debut"
            type="time"
            value={formData.heure_debut}
            onChange={handleInputChange}
            leftIcon={<Clock size={16} />}
          />

          <Input
            label="Heure de fin"
            name="heure_fin"
            type="time"
            value={formData.heure_fin}
            onChange={handleInputChange}
            leftIcon={<Clock size={16} />}
          />
        </div>

        {/* Lieu et capacité */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Lieu"
            name="lieu"
            value={formData.lieu}
            onChange={handleInputChange}
            leftIcon={<MapPin size={16} />}
            placeholder="Salle, terrain, etc."
          />

          <Input
            label="Capacité maximale"
            name="capacite_max"
            type="number"
            value={formData.capacite_max}
            onChange={handleInputChange}
            leftIcon={<Users size={16} />}
            min="1"
          />
        </div>

        {/* Prix et responsable */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Prix (DH)"
            name="prix"
            type="number"
            value={formData.prix}
            onChange={handleInputChange}
            required
            leftIcon={<DollarSign size={16} />}
            min="0"
            step="0.01"
          />

          <Select
            label="Responsable"
            name="id_responsable"
            value={formData.id_responsable}
            onChange={handleInputChange}
            options={responsableOptions}
          />
        </div>

        {/* Boutons */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            icon={<Save size={16} />}
          >
            {activite ? 'Modifier' : 'Créer'}
          </Button>
        </div>
      </form>
      </div>
    </div>
  );
};

export default ActiviteForm;
