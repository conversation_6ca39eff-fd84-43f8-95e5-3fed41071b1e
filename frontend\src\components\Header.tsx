import { Bell, MessageCircle, Menu, X, LogOut, Lock, User } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  title: string;
  onMenuClick?: () => void;
  isMobileMenuOpen?: boolean;
}

export default function Header({ title, onMenuClick, isMobileMenuOpen }: HeaderProps) {
  // const user = JSON.parse(localStorage.getItem("user") || '{}');
  const { user, setUser } = useAuth();
  const navigate = useNavigate();
  const [menuOpen, setMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Fermer au clic extérieur
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Fermer au clavier
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setMenuOpen(false);
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Déconnexion
  const handleLogout = () => {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    setUser(null);
    navigate("/login");
  };

  return (
    <div className="h-16 w-full md:w-[92%] lg:w-[85%] px-6 flex items-center justify-between bg-white border-b border-gray-200 fixed top-0 right-0 z-20">
      <div className="flex items-center">
        {/* Bouton burger pour mobile */}
        <button
          aria-label="Ouvrir le menu mobile"
          onClick={onMenuClick}
          className="md:hidden pr-4  text-gray-500 hover:text-primary hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
        <h1 className="text-md md:text-2xl font-bold text-secondary">{title}</h1>
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center">
          {/* Notifications */}
          <button 
            aria-label="Notifications"
            className="p-2 rounded-full text-gray-500 hover:text-primary hover:bg-blue-50 relative"
          >
            <Bell size={20} />
            <span className="absolute -top-[1px] -right-[1px] text-[10px] bg-red-500 text-white rounded-full px-1.5">
              3
            </span>
          </button>
          {/* Messages */}
          <button 
            aria-label="Messages"
            className="p-2 rounded-full text-gray-500 hover:text-primary hover:bg-blue-50 relative mx-2"
          >
            <MessageCircle size={20} />
            <span className="absolute -top-[1px] -right-[1px] text-[10px] bg-red-500 text-white rounded-full px-1.5">
              2
            </span>
          </button>

          {/* Profile */}
          <div className="relative" ref={menuRef}>
            <button
              onClick={() => setMenuOpen(!menuOpen)}
              className="flex items-center space-x-2 bg-primary text-white w-9 h-9 rounded-full justify-center font-semibold text-sm focus:outline-none"
              aria-label="Menu utilisateur"
            >
              <span>
                {user?.nom && user?.prenom
                  ? `${user.nom[0].toUpperCase()}${user.prenom[0].toUpperCase()}`
                  : "U"}
              </span>
            </button>

            {/* Dropdown */}
            {menuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-30 text-sm border">
                <div className="px-4 py-2 font-semibold text-gray-700 w-full text-left hover:bg-gray-100 flex items-center gap-2">
                  <User size={16} /> {user?.nom} {user?.prenom}
                </div>
                <button
                  onClick={() => {
                    navigate("/change-password");
                    setMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2"
                >
                  <Lock size={16} /> Changer mot de passe
                </button>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 flex items-center gap-2"
                >
                  <LogOut size={16} /> Déconnexion
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};