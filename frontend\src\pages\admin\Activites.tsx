import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Users, Clock, MapPin, DollarSign, Calendar, Activity } from 'lucide-react';
import Button from '../../components/Button';
import Select from '../../components/Select';
import { getActivites, deleteActivite, getStatistiquesActivites } from '../../services/api';
import { Activite } from '../../types';
import { useDeleteConfirmation } from '../../hooks/useConfirmation';
import ActiviteForm from '../../components/ActiviteForm';
import ActiviteDetailsModal from '../../components/ActiviteDetailsModal';

const Activites: React.FC = () => {
  const [activites, setActivites] = useState<Activite[]>([]);
  const [filteredActivites, setFilteredActivites] = useState<Activite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedActivite, setSelectedActivite] = useState<Activite | null>(null);
  const [statistiques, setStatistiques] = useState<any>(null);

  // Filtres
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [statutFilter, setStatutFilter] = useState<string>('');
  const [jourFilter, setJourFilter] = useState<string>('');

  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  useEffect(() => {
    fetchData();
    fetchStatistiques();
  }, []);

  useEffect(() => {
    filterActivites();
  }, [activites, searchTerm, typeFilter, statutFilter, jourFilter]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await getActivites();
      setActivites(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des activités:', error);
      setError('Erreur lors du chargement des activités');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistiques = async () => {
    try {
      const response = await getStatistiquesActivites();
      setStatistiques(response.data.data);
    } catch (error: any) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  const filterActivites = () => {
    let filtered = activites;

    // Recherche textuelle
    if (searchTerm) {
      filtered = filtered.filter(activite =>
        activite.nom_activite?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activite.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activite.lieu?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activite.responsable?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activite.responsable?.prenom?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par type
    if (typeFilter) {
      filtered = filtered.filter(activite => activite.type_activite === typeFilter);
    }

    // Filtre par statut
    if (statutFilter) {
      filtered = filtered.filter(activite => activite.statut === statutFilter);
    }

    // Filtre par jour
    if (jourFilter) {
      filtered = filtered.filter(activite => activite.jour_semaine === jourFilter);
    }

    setFilteredActivites(filtered);
  };

  const handleAdd = () => {
    setSelectedActivite(null);
    setShowForm(true);
  };

  const handleEdit = (activite: Activite) => {
    setSelectedActivite(activite);
    setShowForm(true);
  };

  const handleDeleteClick = (id: number) => {
    confirmDelete(
      () => handleDelete(id),
      'cette activité',
      'Êtes-vous sûr de vouloir supprimer cette activité ? Cette action supprimera également toutes les inscriptions associées.'
    );
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteActivite(id);
      await fetchData();
      await fetchStatistiques();
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
      setError('Erreur lors de la suppression de l\'activité');
    }
  };

  const handleViewDetails = (activite: Activite) => {
    setSelectedActivite(activite);
    setShowDetailsModal(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setSelectedActivite(null);
    fetchData();
    fetchStatistiques();
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setTypeFilter('');
    setStatutFilter('');
    setJourFilter('');
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sport': return 'bg-green-100 text-green-800';
      case 'artistique': return 'bg-purple-100 text-purple-800';
      case 'culturelle': return 'bg-blue-100 text-blue-800';
      case 'scientifique': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'terminee': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatJour = (jour: string) => {
    const jours: { [key: string]: string } = {
      'lundi': 'Lundi',
      'mardi': 'Mardi',
      'mercredi': 'Mercredi',
      'jeudi': 'Jeudi',
      'vendredi': 'Vendredi',
      'samedi': 'Samedi'
    };
    return jours[jour] || jour;
  };

  const formatType = (type: string) => {
    const types: { [key: string]: string } = {
      'sport': 'Sportive',
      'artistique': 'Artistique',
      'culturelle': 'Culturelle',
      'scientifique': 'Scientifique',
      'autre': 'Autre'
    };
    return types[type] || type;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Activités</h1>
          <p className="text-gray-600">Gérez les activités extrascolaires et les inscriptions</p>
        </div>

        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Rechercher..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <Button
            icon={<Filter size={16} />}
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filtres
          </Button>

          <Button
            icon={<Plus size={16} />}
            variant="primary"
            onClick={handleAdd}
          >
            Nouvelle activité
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      {statistiques && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Activity className="w-8 h-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Activités</p>
                <p className="text-2xl font-bold text-gray-900">{statistiques.total_activites}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Inscriptions</p>
                <p className="text-2xl font-bold text-gray-900">{statistiques.total_inscriptions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Plus Populaire</p>
                <p className="text-lg font-bold text-gray-900 truncate">{statistiques.activite_populaire}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Taux Occupation</p>
                <p className="text-2xl font-bold text-gray-900">{statistiques.taux_occupation}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Panneau de filtres */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex flex-wrap items-center gap-3 justify-start">
            {/* Filtre par type */}
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="flex-1 min-w-[140px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les types</option>
              <option value="sport">Sportive</option>
              <option value="artistique">Artistique</option>
              <option value="culturelle">Culturelle</option>
              <option value="scientifique">Scientifique</option>
              <option value="autre">Autre</option>
            </select>

            {/* Filtre par statut */}
            <select
              value={statutFilter}
              onChange={(e) => setStatutFilter(e.target.value)}
              className="flex-1 min-w-[120px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les statuts</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="terminee">Terminée</option>
            </select>

            {/* Filtre par jour */}
            <select
              value={jourFilter}
              onChange={(e) => setJourFilter(e.target.value)}
              className="flex-1 min-w-[120px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les jours</option>
              <option value="lundi">Lundi</option>
              <option value="mardi">Mardi</option>
              <option value="mercredi">Mercredi</option>
              <option value="jeudi">Jeudi</option>
              <option value="vendredi">Vendredi</option>
              <option value="samedi">Samedi</option>
            </select>

            {/* Bouton Réinitialiser */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetFilters}
            >
              Réinitialiser
            </Button>
          </div>
        </div>
      )}

      {/* Message d'erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Tableau des activités */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Liste des activités ({filteredActivites.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Activité
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Planning
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lieu
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Participants
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Prix
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredActivites.map((activite) => (
                <tr key={activite.id_activite} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {activite.nom_activite}
                      </div>
                      {activite.responsable && (
                        <div className="text-sm text-gray-500">
                          {activite.responsable.prenom} {activite.responsable.nom}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(activite.type_activite)}`}>
                      {formatType(activite.type_activite)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                        {formatJour(activite.jour_semaine)}
                      </div>
                      <div className="flex items-center mt-1">
                        <Clock className="w-4 h-4 text-gray-400 mr-1" />
                        {activite.heure_debut} - {activite.heure_fin}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                      {activite.lieu}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <Users className="w-4 h-4 text-gray-400 mr-1" />
                      {activite.nombre_inscrits || 0}/{activite.capacite_max}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <DollarSign className="w-4 h-4 text-gray-400 mr-1" />
                      {activite.prix} DH
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatutColor(activite.statut)}`}>
                      {activite.statut}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleViewDetails(activite)}
                        className="text-blue-600 hover:text-blue-900"
                        title="Voir les détails"
                      >
                        <Users size={16} />
                      </button>
                      <button
                        onClick={() => handleEdit(activite)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(activite.id_activite)}
                        className="text-red-600 hover:text-red-900"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredActivites.length === 0 && (
            <div className="text-center py-12">
              <Activity className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune activité</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || typeFilter || statutFilter || jourFilter
                  ? "Aucune activité ne correspond aux critères de recherche."
                  : "Commencez par créer une nouvelle activité."}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {showForm && (
        <ActiviteForm
          activite={selectedActivite}
          onClose={() => setShowForm(false)}
          onSuccess={handleFormSuccess}
        />
      )}

      {showDetailsModal && selectedActivite && (
        <ActiviteDetailsModal
          activite={selectedActivite}
          onClose={() => setShowDetailsModal(false)}
          onRefresh={fetchData}
        />
      )}

      <ConfirmationComponent />
    </div>
  );
};

export default Activites;
