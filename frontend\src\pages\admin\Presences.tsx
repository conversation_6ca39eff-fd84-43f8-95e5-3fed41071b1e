import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Calendar, Users, Clock, AlertTriangle, CheckCircle, Edit, Trash2 } from 'lucide-react';
import Button from '../../components/Button';
import Table from '../../components/Table';
import Pagination from '../../components/Pagination';
import { usePagination } from '../../hooks/usePagination';
import { 
  getAbsencesEleves, 
  deleteAbsenceEleve, 
  getStatistiquesAbsencesEleves,
  getClasses 
} from '../../services/api';
import { AbsenceEleve } from '../../types';
import AbsenceEleveForm from '../../components/AbsenceEleveForm';

const PresencesPage: React.FC = () => {
  const [absences, setAbsences] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [selectedAbsence, setSelectedAbsence] = useState<any>(null);
  const [statistics, setStatistics] = useState<any>(null);

  // Filtres
  const [filters, setFilters] = useState({
    date_debut: '',
    date_fin: '',
    id_classe: '',
    motif: '',
    justifiee: '',
    search: ''
  });

  useEffect(() => {
    fetchAbsences();
    fetchClasses();
    fetchStatistics();
  }, []);

  const fetchAbsences = async () => {
    try {
      setLoading(true);
      const apiFilters: any = {};
      
      if (filters.date_debut) apiFilters.date_debut = filters.date_debut;
      if (filters.date_fin) apiFilters.date_fin = filters.date_fin;
      if (filters.id_classe) apiFilters.id_classe = parseInt(filters.id_classe);
      if (filters.motif) apiFilters.motif = filters.motif;
      if (filters.justifiee !== '') apiFilters.justifiee = filters.justifiee === 'true';

      const response = await getAbsencesEleves(apiFilters);
      setAbsences(response.data.data || []);
      setError(null);
    } catch (error: any) {
      console.error('Erreur lors du chargement des absences:', error);
      setError('Erreur lors du chargement des absences');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await getClasses();
      setClasses(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des classes:', error);
    }
  };

  const fetchStatistics = async () => {
    try {
      const apiFilters: any = {};
      if (filters.date_debut) apiFilters.date_debut = filters.date_debut;
      if (filters.date_fin) apiFilters.date_fin = filters.date_fin;
      if (filters.id_classe) apiFilters.id_classe = parseInt(filters.id_classe);

      const response = await getStatistiquesAbsencesEleves(apiFilters);
      setStatistics(response.data.data || {});
    } catch (error: any) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  const handleDelete = async (id: number, eleveNom: string) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'absence de ${eleveNom} ?`)) {
      try {
        await deleteAbsenceEleve(id);
        setSuccessMessage('Absence supprimée avec succès');
        await fetchAbsences();
        await fetchStatistics();
        
        setTimeout(() => setSuccessMessage(null), 3000);
      } catch (error: any) {
        console.error('Erreur lors de la suppression:', error);
        setError(error.response?.data?.message || 'Erreur lors de la suppression de l\'absence');
      }
    }
  };

  const handleEdit = (absence: AbsenceEleve) => {
    setSelectedAbsence(absence);
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setSelectedAbsence(null);
    fetchAbsences();
    fetchStatistics();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    fetchAbsences();
    fetchStatistics();
    resetPagination(); // Réinitialiser la pagination lors de l'application des filtres
  };

  const resetFilters = () => {
    setFilters({
      date_debut: '',
      date_fin: '',
      id_classe: '',
      motif: '',
      justifiee: '',
      search: ''
    });
  };

  // Filtrage côté client pour la recherche textuelle
  const filteredAbsences = absences.filter((absence: any) => {
    if (!filters.search) return true;
    const searchLower = filters.search.toLowerCase();
    return (
      absence.nom?.toLowerCase().includes(searchLower) ||
      absence.prenom?.toLowerCase().includes(searchLower) ||
      absence.code_massar?.toLowerCase().includes(searchLower) ||
      absence.nom_classe?.toLowerCase().includes(searchLower)
    );
  });

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedAbsences,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredAbsences,
    initialItemsPerPage: 10
  });

  // Configuration des colonnes pour le tableau (format Élèves)
  const columns = [
    { header: "Élève", accessor: "eleve", className: "p-3" },
    { header: "Période", accessor: "periode", className: "p-3" },
    { header: "Durée", accessor: "duree", className: "p-3" },
    { header: "Motif", accessor: "motif", className: "p-3" },
    { header: "Statut", accessor: "statut", className: "p-3" },
    { header: "Actions", accessor: "actions", className: "p-3" },
  ];



  const renderRow = (absence: any) => (
    <tr key={absence.id_absence_eleve} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3">
        <div className="flex items-center">
          <Users className="w-5 h-5 text-gray-400 mr-3" />
          <div>
            <div className="text-sm font-medium text-gray-900">
              {absence.prenom} {absence.nom}
            </div>
            <div className="text-sm text-gray-500">
              {absence.code_massar} • {absence.nom_classe}
            </div>
          </div>
        </div>
      </td>
      <td className="p-3">
        <div className="flex items-center">
          <Calendar className="w-4 h-4 text-gray-400 mr-3" />
          <div>
            <div className="text-sm text-gray-900">
              de {new Date(absence.date_debut).toLocaleDateString('fr-FR')}
            </div>
            {absence.date_fin && absence.date_fin !== absence.date_debut && (
              <div className="text-sm text-gray-900">
                au {new Date(absence.date_fin).toLocaleDateString('fr-FR')}
              </div>
            )}
          </div>
        </div>
      </td>
      <td className="p-3">
        <div className="flex items-center">
          <Clock className="w-4 h-4 text-gray-400 mr-1" />
          <span className="text-sm text-gray-900">
            {absence.duree ? formatDuree(absence.duree) : '-'}
          </span>
        </div>
      </td>
      <td className="p-3">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          absence.motif === 'maladie' ? 'bg-red-100 text-red-800' :
          absence.motif === 'familial' ? 'bg-blue-100 text-blue-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {absence.motif || 'Non spécifié'}
        </span>
      </td>
      <td className="p-3">
        <div className="flex items-center">
          {absence.justifiee ? (
            <div className="flex items-center text-green-600">
              <CheckCircle className="w-4 h-4 mr-1" />
              <span className="text-sm">Justifiée</span>
            </div>
          ) : (
            <div className="flex items-center text-red-600">
              <AlertTriangle className="w-4 h-4 mr-1" />
              <span className="text-sm">Non justifiée</span>
            </div>
          )}
        </div>
      </td>
      <td className="p-3">
        <div className="flex space-x-2">
          <button
            className="text-indigo-600 hover:text-indigo-900"
            onClick={() => handleEdit(absence)}
            title="Modifier"
          >
            <Edit size={16} />
          </button>
          <button
            className="text-red-600 hover:text-red-900"
            onClick={() => handleDelete(absence.id_absence_eleve, `${absence.prenom} ${absence.nom}`)}
            title="Supprimer"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </td>
    </tr>
  );





  const formatDuree = (dureeMinutes: number) => {
    if (dureeMinutes < 60) {
      return `${dureeMinutes} min`;
    }
    const heures = Math.floor(dureeMinutes / 60);
    const minutes = dureeMinutes % 60;
    return minutes > 0 ? `${heures}h ${minutes}min` : `${heures}h`;
  };

  return (
    <div className="space-y-6">
      
      {/* Statistiques */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Absences</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.total_absences || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Élèves Absents</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.eleves_absents || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Justifiées</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.absences_justifiees || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Durée Totale</p>
                <p className="text-2xl font-bold text-gray-900">
                  {statistics.duree_totale ? formatDuree(statistics.duree_totale) : '0h'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
          {successMessage}
        </div>
      )}
      {/* Filtres et recherche */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Barre de recherche */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher par nom, prénom, code Massar ou classe..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Boutons d'action */}
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <Filter size={16} />
              Filtres
              {showFilters && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-1">Actifs</span>}
            </button>

            <Button
              onClick={() => setShowForm(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Plus size={16} />
              Nouvelle Absence
            </Button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date début</label>
                <input
                  type="date"
                  value={filters.date_debut}
                  onChange={(e) => handleFilterChange('date_debut', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date fin</label>
                <input
                  type="date"
                  value={filters.date_fin}
                  onChange={(e) => handleFilterChange('date_fin', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Classe</label>
                <select
                  value={filters.id_classe}
                  onChange={(e) => handleFilterChange('id_classe', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Toutes les classes</option>
                  {classes.map(classe => (
                    <option key={classe.id_classe} value={classe.id_classe.toString()}>
                      {classe.nom_classe}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Motif</label>
                <select
                  value={filters.motif}
                  onChange={(e) => handleFilterChange('motif', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Tous les motifs</option>
                  <option value="maladie">Maladie</option>
                  <option value="familial">Familial</option>
                  <option value="autre">Autre</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Justification</label>
                <select
                  value={filters.justifiee}
                  onChange={(e) => handleFilterChange('justifiee', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Toutes</option>
                  <option value="true">Justifiées</option>
                  <option value="false">Non justifiées</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-4">
              <button
                onClick={() => {
                  resetFilters();
                  setTimeout(applyFilters, 100);
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Réinitialiser
              </button>
              <button
                onClick={applyFilters}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Appliquer les filtres
              </button>
            </div>
          </div>
        )}
      {/* </div> */}

      {/* Liste des absences */}
      <div className="">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : paginatedAbsences.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="mx-auto mb-2 w-10 h-10" />
            <p>Aucune absence trouvée</p>
            {(filters.search || filters.date_debut || filters.date_fin || filters.id_classe || filters.motif || filters.justifiee) && (
              <p className="text-sm">
                Essayez de modifier votre recherche ou{" "}
                <button onClick={() => {
                  setFilters({
                    search: '',
                    date_debut: '',
                    date_fin: '',
                    id_classe: '',
                    motif: '',
                    justifiee: ''
                  });
                }} className="text-blue-600 underline">
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <Table columns={columns} data={paginatedAbsences} renderRow={renderRow} />
          {/* Pagination */}
          {filteredAbsences.length > 0 && (
            <div className="px-6 py-3 border-gray-200 text-sm">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={setCurrentPage}
                onItemsPerPageChange={setItemsPerPage}
                showItemsPerPage={true}
              />
            </div>
          )}
        </div>
        )}
      </div>

      {/* Modal de formulaire */}
      {showForm && (
        <AbsenceEleveForm
          absence={selectedAbsence}
          onClose={() => {
            setShowForm(false);
            setSelectedAbsence(null);
          }}
          onSuccess={handleFormSuccess}
        />
      )}
    </div>
  );
};

export default PresencesPage;
