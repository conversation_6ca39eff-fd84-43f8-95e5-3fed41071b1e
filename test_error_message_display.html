<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Affichage Message d'Erreur Transport</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Test Affichage Message d'Erreur Transport</h1>
        
        <!-- Simulation du message d'erreur tel qu'il apparaît dans l'interface -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Formulaire de Paiement</h2>
            
            <!-- Messages d'erreur -->
            <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Erreur de validation
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>Cet élève ne bénéficie pas du transport scolaire. Impossible de créer un paiement de transport.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Simulation du formulaire -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Élève</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-600 focus:border-blue-600">
                        <option>Ahmed Ben Ali - 12345</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Type de paiement</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-600 focus:border-blue-600">
                        <option selected>Transport</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Montant (DH)</label>
                    <input type="number" value="150" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-600 focus:border-blue-600">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mois</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-600 focus:border-blue-600">
                        <option>Janvier</option>
                    </select>
                </div>
            </div>

            <!-- Boutons -->
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    Annuler
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Créer le paiement
                </button>
            </div>
        </div>

        <!-- Informations sur le test -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-800 mb-2">Informations sur le test</h3>
            <div class="text-sm text-blue-700">
                <p><strong>Message attendu :</strong> "Cet élève ne bénéficie pas du transport scolaire. Impossible de créer un paiement de transport."</p>
                <p><strong>Contexte :</strong> Ce message doit s'afficher quand un utilisateur tente de créer un paiement de transport pour un élève qui ne bénéficie pas du transport scolaire.</p>
                <p><strong>Style :</strong> Fond rouge clair, bordure rouge, icône d'erreur, texte rouge foncé</p>
            </div>
        </div>

        <!-- Instructions de test -->
        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-yellow-800 mb-2">Instructions de test dans l'application</h3>
            <div class="text-sm text-yellow-700">
                <ol class="list-decimal pl-5 space-y-1">
                    <li>Aller sur <code>/admin/paiements</code></li>
                    <li>Cliquer sur "Nouveau Paiement"</li>
                    <li>Sélectionner Type de paiement = "Transport"</li>
                    <li>Si aucun élève avec transport : voir message d'avertissement jaune</li>
                    <li>Si tentative de contournement : voir message d'erreur rouge ci-dessus</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
