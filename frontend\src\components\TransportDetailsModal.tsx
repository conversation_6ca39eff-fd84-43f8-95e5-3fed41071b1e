import React, { useState, useEffect } from 'react';
import { X, Plus, Search, Users, Bus, DollarSign, Trash2, User } from 'lucide-react';
import Button from './Button';
import { 
  getTransportEleves, 
  getEleves, 
  inscrireEleveTransport, 
  desinscrireEleveTransport 
} from '../services/api';
import { Transport, Eleve } from '../types';

interface TransportDetailsModalProps {
  transport: Transport;
  onClose: () => void;
  onRefresh: () => void;
}

const TransportDetailsModal: React.FC<TransportDetailsModalProps> = ({ 
  transport, 
  onClose, 
  onRefresh 
}) => {
  const [elevesInscrits, setElevesInscrits] = useState<any[]>([]);
  const [elevesDisponibles, setElevesDisponibles] = useState<Eleve[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchElevesInscrits();
    fetchElevesDisponibles();
  }, [transport.id_transport]);

  const fetchElevesInscrits = async () => {
    try {
      setLoading(true);
      const response = await getTransportEleves(transport.id_transport);
      setElevesInscrits(response.data.data || []);
      setError(null);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves inscrits:', error);
      setError('Erreur lors du chargement des élèves inscrits');
    } finally {
      setLoading(false);
    }
  };

  const fetchElevesDisponibles = async () => {
    try {
      const response = await getEleves();
      setElevesDisponibles(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves disponibles:', error);
    }
  };

  const handleInscrireEleve = async (idEleve: number) => {
    try {
      await inscrireEleveTransport(transport.id_transport, idEleve);
      setSuccessMessage('Élève inscrit avec succès');
      await fetchElevesInscrits();
      await fetchElevesDisponibles();
      onRefresh();
      
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      console.error('Erreur lors de l\'inscription:', error);
      setError(error.response?.data?.message || 'Erreur lors de l\'inscription de l\'élève');
    }
  };

  const handleDesinscrireEleve = async (idEleve: number, nomEleve: string) => {
    if (window.confirm(`Êtes-vous sûr de vouloir désinscrire ${nomEleve} de ce transport ?`)) {
      try {
        await desinscrireEleveTransport(transport.id_transport, idEleve);
        setSuccessMessage('Élève désinscrit avec succès');
        await fetchElevesInscrits();
        await fetchElevesDisponibles();
        onRefresh();
        
        setTimeout(() => setSuccessMessage(null), 3000);
      } catch (error: any) {
        console.error('Erreur lors de la désinscription:', error);
        setError(error.response?.data?.message || 'Erreur lors de la désinscription de l\'élève');
      }
    }
  };

  const getElevesNonInscrits = () => {
    const idsInscrits = elevesInscrits.map(e => e.id_eleve);
    return elevesDisponibles.filter(eleve => !idsInscrits.includes(eleve.id_eleve));
  };

  const filteredElevesNonInscrits = getElevesNonInscrits().filter(eleve => {
    const nomComplet = eleve.user ? `${eleve.user.nom} ${eleve.user.prenom}` : '';
    return nomComplet.toLowerCase().includes(searchTerm.toLowerCase()) ||
           eleve.code_massar?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getOccupationColor = (taux: number) => {
    if (taux <= 50) return 'text-green-600 bg-green-100';
    if (taux <= 80) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-6xl max-h-[95vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {transport.trajet}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Matricule: {transport.matricule}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {/* Messages */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}

            {successMessage && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {successMessage}
              </div>
            )}

            {/* Informations du transport */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <Bus className="w-8 h-8 text-blue-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Capacité</p>
                    <p className="text-lg font-bold text-gray-900">{transport.capacite} places</p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <Users className="w-8 h-8 text-green-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Inscrits</p>
                    <p className="text-lg font-bold text-gray-900">{elevesInscrits.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <DollarSign className="w-8 h-8 text-purple-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Prix</p>
                    <p className="text-lg font-bold text-gray-900">{transport.prix} DH</p>
                  </div>
                </div>
              </div>

              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 flex items-center justify-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getOccupationColor(transport.taux_occupation || 0)}`}>
                      {Math.round(transport.taux_occupation || 0)}%
                    </span>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500">Occupation</p>
                    <p className="text-lg font-bold text-gray-900">
                      {transport.places_disponibles || 0} libres
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Liste des élèves inscrits */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Élèves inscrits ({elevesInscrits.length})
                </h3>
                <Button
                  icon={<Plus size={16} />}
                  variant="primary"
                  size="sm"
                  onClick={() => setShowAddModal(true)}
                  disabled={elevesInscrits.length >= transport.capacite}
                >
                  Inscrire un élève
                </Button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : elevesInscrits.length > 0 ? (
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Élève
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Classe
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Année scolaire
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {elevesInscrits.map((eleve) => (
                        <tr key={eleve.id_eleve} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <User className="w-8 h-8 text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {eleve.prenom} {eleve.nom}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {eleve.code_massar}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {eleve.nom_classe}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {eleve.annee_scolaire}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => handleDesinscrireEleve(eleve.id_eleve, `${eleve.prenom} ${eleve.nom}`)}
                              className="text-red-600 hover:text-red-900"
                              title="Désinscrire"
                            >
                              <Trash2 size={16} />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun élève inscrit</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Commencez par inscrire des élèves à ce transport.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modal d'ajout d'élève */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[95vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Inscrire un élève
              </h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    placeholder="Rechercher un élève..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {filteredElevesNonInscrits.length > 0 ? (
                  <div className="space-y-2">
                    {filteredElevesNonInscrits.map((eleve) => (
                      <div
                        key={eleve.id_eleve}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center">
                          <User className="w-8 h-8 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {eleve.user ? `${eleve.user.prenom} ${eleve.user.nom}` : 'Nom non disponible'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {eleve.code_massar}
                            </div>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="primary"
                          onClick={() => {
                            handleInscrireEleve(eleve.id_eleve);
                            setShowAddModal(false);
                          }}
                        >
                          Inscrire
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      {searchTerm ? 'Aucun élève trouvé' : 'Tous les élèves sont déjà inscrits'}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm ? 'Essayez avec d\'autres termes de recherche.' : 'Ce transport est complet.'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TransportDetailsModal;
