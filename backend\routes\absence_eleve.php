<?php
// routes/absence_eleve.php

require_once __DIR__ . '/../controllers/AbsenceEleveController.php';

function handleAbsenceEleveRoutes($uri, $method, $pdo) {
    $controller = new AbsenceEleveController();
    
    // GET /absences-eleves/statistiques - Récupérer les statistiques
    if ($uri === '/absences-eleves/statistiques' && $method === 'GET') {
        $controller->getStatistiques();
        return true;
    }
    
    // GET /absences-eleves - Récupérer toutes les absences
    if ($uri === '/absences-eleves' && $method === 'GET') {
        $controller->index();
        return true;
    }
    
    // POST /absences-eleves - Créer une nouvelle absence
    if ($uri === '/absences-eleves' && $method === 'POST') {
        $controller->store();
        return true;
    }
    
    // GET /absences-eleves/{id} - Récupérer une absence par ID
    if (preg_match('/^\/absences-eleves\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = $matches[1];
        $controller->show($id);
        return true;
    }
    
    // PUT /absences-eleves/{id} - Mettre à jour une absence
    if (preg_match('/^\/absences-eleves\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = $matches[1];
        $controller->update($id);
        return true;
    }
    
    // DELETE /absences-eleves/{id} - Supprimer une absence
    if (preg_match('/^\/absences-eleves\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = $matches[1];
        $controller->destroy($id);
        return true;
    }
    
    // GET /absences-eleves/eleve/{id} - Récupérer les absences d'un élève
    if (preg_match('/^\/absences-eleves\/eleve\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id_eleve = $matches[1];
        $controller->getByEleve($id_eleve);
        return true;
    }
    
    // GET /absences-eleves/classe/{id} - Récupérer les absences d'une classe
    if (preg_match('/^\/absences-eleves\/classe\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id_classe = $matches[1];
        $controller->getByClasse($id_classe);
        return true;
    }
    
    return false;
}
?>
