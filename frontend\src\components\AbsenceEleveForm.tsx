import React, { useState, useEffect } from 'react';
import { X, Save, Calendar, Users, Clock, AlertTriangle } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import Select from './Select';
import { addAbsenceEleve, updateAbsenceEleve, getEleves, getClasses } from '../services/api';
import { AbsenceEleve } from '../types';

interface AbsenceEleveFormProps {
  absence?: AbsenceEleve | null;
  onClose: () => void;
  onSuccess: () => void;
}

const AbsenceEleveForm: React.FC<AbsenceEleveFormProps> = ({ absence, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [eleves, setEleves] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);

  const [formData, setFormData] = useState({
    id_eleve: '',
    id_classe: '',
    date_debut: '',
    heure_debut: '',
    date_fin: '',
    heure_fin: '',
    motif: 'maladie' as 'maladie' | 'familial' | 'autre',
    justifiee: false,
    commentaire: ''
  });

  useEffect(() => {
    fetchEleves();
    fetchClasses();
    
    if (absence) {
      const dateDebut = new Date(absence.date_debut);
      const dateFin = new Date(absence.date_fin);
      
      setFormData({
        id_eleve: absence.id_eleve.toString(),
        id_classe: absence.id_classe.toString(),
        date_debut: dateDebut.toISOString().split('T')[0],
        heure_debut: dateDebut.toTimeString().slice(0, 5),
        date_fin: dateFin.toISOString().split('T')[0],
        heure_fin: dateFin.toTimeString().slice(0, 5),
        motif: absence.motif,
        justifiee: absence.justifiee,
        commentaire: absence.commentaire || ''
      });
    }
  }, [absence]);

  const fetchEleves = async () => {
    try {
      const response = await getEleves();
      setEleves(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves:', error);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await getClasses();
      setClasses(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des classes:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleEleveChange = (eleveId: string) => {
    setFormData(prev => ({ ...prev, id_eleve: eleveId }));

    // Trouver la classe de l'élève sélectionné
    const eleve = eleves.find((e: any) => e.id_eleve.toString() === eleveId);
    if (eleve && eleve.classe_actuelle) {
      setFormData(prev => ({ ...prev, id_classe: eleve.classe_actuelle.id_classe.toString() }));
    }
  };

  const validateForm = () => {
    if (!formData.id_eleve) {
      setError('Veuillez sélectionner un élève');
      return false;
    }
    if (!formData.id_classe) {
      setError('Veuillez sélectionner une classe');
      return false;
    }
    if (!formData.date_debut || !formData.heure_debut) {
      setError('Veuillez saisir la date et heure de début');
      return false;
    }
    if (!formData.date_fin || !formData.heure_fin) {
      setError('Veuillez saisir la date et heure de fin');
      return false;
    }

    const dateDebut = new Date(`${formData.date_debut}T${formData.heure_debut}`);
    const dateFin = new Date(`${formData.date_fin}T${formData.heure_fin}`);

    if (dateDebut >= dateFin) {
      setError('La date de fin doit être postérieure à la date de début');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const dataToSubmit = {
        id_eleve: parseInt(formData.id_eleve),
        id_classe: parseInt(formData.id_classe),
        date_debut: `${formData.date_debut} ${formData.heure_debut}:00`,
        date_fin: `${formData.date_fin} ${formData.heure_fin}:00`,
        motif: formData.motif,
        justifiee: formData.justifiee,
        commentaire: formData.commentaire || undefined
      };

      if (absence) {
        await updateAbsenceEleve(absence.id_absence_eleve, dataToSubmit);
        setSuccessMessage('Absence modifiée avec succès');
      } else {
        await addAbsenceEleve(dataToSubmit);
        setSuccessMessage('Absence créée avec succès');
      }

      setTimeout(() => {
        onSuccess();
      }, 1500);

    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(error.response?.data?.message || 'Erreur lors de la sauvegarde de l\'absence');
    } finally {
      setLoading(false);
    }
  };

  const eleveOptions = [
    { value: '', label: 'Sélectionner un élève' },
    ...eleves.map((eleve: any) => ({
      value: eleve.id_eleve.toString(),
      label: `${eleve.user?.prenom} ${eleve.user?.nom} - ${eleve.code_massar}`
    }))
  ];

  const classeOptions = [
    { value: '', label: 'Sélectionner une classe' },
    ...classes.map((classe: any) => ({
      value: classe.id_classe.toString(),
      label: classe.nom_classe
    }))
  ];

  const motifOptions = [
    { value: 'maladie', label: 'Maladie' },
    { value: 'familial', label: 'Raison familiale' },
    { value: 'autre', label: 'Autre' }
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[95vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {absence ? 'Modifier l\'absence' : 'Nouvelle absence'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {successMessage}
            </div>
          )}

          {/* Informations de l'élève */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Informations de l'élève
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Élève"
                name="id_eleve"
                value={formData.id_eleve}
                onChange={(e) => handleEleveChange(e.target.value)}
                required
                options={eleveOptions}
              />

              <Select
                label="Classe"
                name="id_classe"
                value={formData.id_classe}
                onChange={handleInputChange}
                required
                options={classeOptions}
              />
            </div>
          </div>

          {/* Période d'absence */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Période d'absence
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Date de début</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="date"
                    name="date_debut"
                    value={formData.date_debut}
                    onChange={handleInputChange}
                    required
                  />
                  <Input
                    type="time"
                    name="heure_debut"
                    value={formData.heure_debut}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Date de fin</label>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="date"
                    name="date_fin"
                    value={formData.date_fin}
                    onChange={handleInputChange}
                    required
                  />
                  <Input
                    type="time"
                    name="heure_fin"
                    value={formData.heure_fin}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Détails de l'absence */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Détails de l'absence
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Motif"
                name="motif"
                value={formData.motif}
                onChange={handleInputChange}
                required
                options={motifOptions}
              />

              <div className="flex items-center space-x-3 pt-6">
                <input
                  type="checkbox"
                  id="justifiee"
                  name="justifiee"
                  checked={formData.justifiee}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="justifiee" className="text-sm font-medium text-gray-700">
                  Absence justifiée
                </label>
              </div>
            </div>

            <div>
              <label htmlFor="commentaire" className="block text-sm font-medium text-gray-700 mb-2">
                Commentaire (optionnel)
              </label>
              <textarea
                id="commentaire"
                name="commentaire"
                value={formData.commentaire}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-600 focus:border-blue-600"
                placeholder="Détails supplémentaires sur l'absence..."
              />
            </div>
          </div>

          {/* Informations importantes */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              Informations importantes
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• La durée sera calculée automatiquement</li>
              <li>• Vérifiez que les dates et heures sont correctes</li>
              <li>• Une absence justifiée nécessite un document officiel</li>
              <li>• Les absences répétées doivent être signalées</li>
            </ul>
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
              icon={<Save size={16} />}
            >
              {loading ? 'Enregistrement...' : (absence ? 'Modifier' : 'Créer')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AbsenceEleveForm;
