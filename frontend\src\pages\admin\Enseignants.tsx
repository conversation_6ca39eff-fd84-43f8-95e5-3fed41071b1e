import React, { useEffect, useState } from "react";
import { getEnseignants, deleteEnseignant, getMatieres, getClasses } from "../../services/api";
import { useAuth } from "../../context/AuthContext";
import StandardTable from "../../components/StandardTable";
import Modal from "../../components/Modal";
import Button from "../../components/Button";
import Select from "../../components/Select";
import StatusSelect from "../../components/StatusSelect";
import { Search, Filter, Plus, Eye, Edit, Trash2, Users, User } from "lucide-react";
import EnseignantForm from "../../components/EnseignantForm";
import EnseignantDetailModal from "../../components/EnseignantDetailModal";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";
import { Enseignant, Matiere, Classe } from "../../types";
import { usePagination } from "../../hooks/usePagination";

export default function Enseignants() {
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();
  const [enseignants, setEnseignants] = useState<Enseignant[]>([]);
  const [enseignantToEdit, setEnseignantToEdit] = useState<Enseignant | null>(null);
  const [enseignantToView, setEnseignantToView] = useState<Enseignant | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(true);

  // États pour les filtres
  const [statutFilter, setStatutFilter] = useState<string>('');
  const [matiereFilter, setMatiereFilter] = useState<string>('');
  const [classeFilter, setClasseFilter] = useState<string>('');
  const [matieres, setMatieres] = useState<Matiere[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  const { user } = useAuth();

  useEffect(() => {
    fetchEnseignants();
    fetchMatieres();
    fetchClasses();
  }, []);

  const fetchMatieres = async () => {
    try {
      const res = await getMatieres();
      setMatieres(res.data.data || []);
    } catch (error) {
      console.error("Erreur lors de la récupération des matières :", error);
    }
  };

  const fetchClasses = async () => {
    try {
      const res = await getClasses();
      setClasses(res.data.data || []);
    } catch (error) {
      console.error("Erreur lors de la récupération des classes :", error);
    }
  };

  const fetchEnseignants = async () => {
    try {
      setLoading(true);
      setErrorMessage("");

      const response = await getEnseignants();
      console.log("API Response:", response.data);

      // Gérer la nouvelle structure de réponse {success: true, data: [...]}
      if (response.data.success && Array.isArray(response.data.data)) {
        console.log("Premier enseignant avec classes:", response.data.data[0]);
        setEnseignants(response.data.data);
      } else if (Array.isArray(response.data)) {
        // Fallback pour l'ancienne structure
        setEnseignants(response.data);
      } else {
        console.error("Structure de réponse inattendue:", response.data);
        setEnseignants([]);
      }

    } catch (error) {
      console.error("Erreur lors du chargement des enseignants:", error);
      setErrorMessage("Impossible de charger les enseignants.");
      setEnseignants([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteEnseignant(id);
      fetchEnseignants();
    } catch (error) {
      console.error("Erreur lors de la suppression :", error);
      setErrorMessage("Impossible de supprimer l'enseignant.");
    }
  };

  const handleDeleteClick = (enseignant: Enseignant) => {
    const enseignantName = `${enseignant.user?.prenom || ''} ${enseignant.user?.nom || ''}`.trim();
    confirmDelete(
      () => handleDelete(enseignant.id_enseignant),
      enseignantName,
      "Cette action supprimera définitivement cet enseignant et toutes ses données associées."
    );
  };

  const handleViewDetails = (enseignant: Enseignant) => {
    setEnseignantToView(enseignant);
    setShowDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setEnseignantToView(null);
  };

  // Configuration des colonnes pour le tableau standard
  const standardColumns = [
    {
      key: "nom_complet",
      label: "Enseignant",
      render: (_: any, row: Enseignant) => (
        <div className="flex items-center">
          <User className="w-8 h-8 text-gray-400 mr-3" />
          <div>
            <div className="font-medium text-gray-900">
              {row.user?.prenom} {row.user?.nom}
            </div>
            <div className="text-sm text-gray-500">{row.num_CIN}</div>
          </div>
        </div>
      )
    },
    {
      key: "contact",
      label: "Contact",
      render: (_: any, row: Enseignant) => (
        <div>
          <div className="text-sm text-gray-900">{row.user?.telephone || "Non renseigné"}</div>
          <div className="text-sm text-gray-500">{row.user?.email || "Non renseigné"}</div>
        </div>
      )
    },
    {
      key: "cnss",
      label: "CNSS",
      render: (_: any, row: Enseignant) => (
        <span className="text-gray-600">{row.num_CNSS || "Non renseigné"}</span>
      )
    },
    {
      key: "matieres",
      label: "Matières",
      render: (_: any, row: Enseignant) => (
        <div className="text-sm text-gray-600">
          {row.matieres?.map(m => m.nom_matiere_fr).join(", ") || "Aucune matière"}
        </div>
      )
    },
    {
      key: "statut",
      label: "Statut",
      render: (_: any, row: Enseignant) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.statut === 'actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {row.statut === 'actif' ? 'Actif' : 'Inactif'}
        </span>
      )
    },
    {
      key: "sexe",
      label: "Sexe",
      render: (_: any, row: Enseignant) => (
        <span className="text-gray-600">{row.user?.sexe === "homme" ? "Homme" : "Femme"}</span>
      )
    }
  ];

  // Actions pour chaque ligne (seulement pour les admins)
  const tableActions = user?.role === "admin" ? [
    {
      label: "Voir",
      icon: <Eye size={16} />,
      onClick: (enseignant: Enseignant) => handleView(enseignant),
      color: "blue" as const
    },
    {
      label: "Modifier",
      icon: <Edit size={16} />,
      onClick: (enseignant: Enseignant) => handleEdit(enseignant),
      color: "indigo" as const
    },
    {
      label: "Supprimer",
      icon: <Trash2 size={16} />,
      onClick: (enseignant: Enseignant) => handleDelete(enseignant.id_enseignant, `${enseignant.user?.prenom} ${enseignant.user?.nom}`),
      color: "red" as const
    }
  ] : [];

  const renderRow = (enseignant: Enseignant) => (
    <tr key={enseignant.id_enseignant} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3 font-semibold">{enseignant.user?.nom || "-"}</td>
      <td className="p-3">{enseignant.user?.prenom || "-"}</td>
      <td className="p-3">{enseignant.num_CIN || "-"}</td>
      <td className="p-3">{enseignant.num_CNSS || "-"}</td>
      <td className="p-3">
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          enseignant.statut === 'actif' ? 'bg-green-100 text-green-800' :
          enseignant.statut === 'terminé' ? 'bg-gray-100 text-gray-800' :
          enseignant.statut === 'suspendu' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {enseignant.statut || 'Sans contrat'}
        </span>
      </td>
      <td className="p-3">
        {enseignant.matieres && enseignant.matieres.length > 0 ? (
          <div className="flex flex-wrap gap-1">
            {enseignant.matieres.map((matiere, index) => (
              <span
                key={index}
                className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
              >
                {matiere.nom_matiere_fr}
              </span>
            ))}
          </div>
        ) : (
          <span className="text-gray-400 text-sm">Aucune matière</span>
        )}
      </td>
      <td className="p-3">{enseignant.user?.telephone || "-"}</td>
      <td className="p-3">
        {enseignant.user?.sexe === "homme"
          ? "H"
          : enseignant.user?.sexe === "femme"
          ? "F"
          : enseignant.user?.sexe === "garçon"
          ? "H"
          : enseignant.user?.sexe === "fille"
          ? "F"
          : "-"}
      </td>
      {user?.role === "admin" && (
        <td className="p-3">
          <div className="flex space-x-2">
            <button
              className="text-blue-600 hover:text-blue-900"
              onClick={() => handleViewDetails(enseignant)}
              title="Voir les détails"
            >
              <Eye size={16} />
            </button>
            <button
              className="text-indigo-600 hover:text-indigo-900"
              onClick={() => {
                setEnseignantToEdit(enseignant);
                setShowModal(true);
              }}
            >
              <Edit size={16} />
            </button>
            <button
              className="text-red-600 hover:text-red-900"
              onClick={() => handleDeleteClick(enseignant)}
              title="Supprimer l'enseignant"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </td>
      )}
    </tr>
  );

  const filteredEnseignants = enseignants.filter((e) => {
    // Filtre par terme de recherche
    const matchesSearch = !searchTerm ||
      e.user?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      e.user?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      e.num_CIN?.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtre par statut
    const matchesStatut = !statutFilter ||
      (statutFilter === 'sans_contrat' && !e.statut) ||
      (e.statut === statutFilter);

    // Filtre par matière (si l'enseignant a des matières)
    const matchesMatiere = !matiereFilter ||
      (e.matieres && e.matieres.some(m => m.id_matiere.toString() === matiereFilter));

    // Filtre par classe (basé sur les cours réellement programmés)
    const matchesClasse = !classeFilter ||
      (e.classes && e.classes.length > 0 && e.classes.some(c => c.id_classe.toString() === classeFilter));

    return matchesSearch && matchesStatut && matchesMatiere && matchesClasse;
  });

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedEnseignants,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredEnseignants,
    initialItemsPerPage: 15
  });

  return (
    <div className="space-y-6">
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher un enseignant..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      
        <div className="flex space-x-2">
          <Button
            icon={<Filter size={16} />}
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filtres
          </Button>
          <Button icon={<Plus size={16} />} variant="primary" onClick={() => {
            setEnseignantToEdit(null);
            setShowModal(true);
          }}>
            Ajouter un enseignant
          </Button>
        </div>
      </div>

      {/* Section des filtres */}
      {showFilters && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <StatusSelect
                label="Statut"
                value={statutFilter}
                onChange={setStatutFilter}
                placeholder="Toutes les statuts"
              />
            </div>

            <div>
              <Select
                label="Matière"
                value={matiereFilter}
                onChange={(e) => setMatiereFilter(e.target.value)}
                placeholder="Toutes les matières"
              >
                {matieres.map((matiere) => (
                  <option key={matiere.id_matiere} value={matiere.id_matiere}>
                    {matiere.nom_matiere_fr}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <Select
                label="Classe"
                value={classeFilter}
                onChange={(e) => setClasseFilter(e.target.value)}
                placeholder="Choisir une classe"
              >
                {classes.map((classe) => (
                  <option key={classe.id_classe} value={classe.id_classe}>
                    {classe.nom_classe}
                  </option>
                ))}
              </Select>
            </div>

            <div className="self-end">
              <Button
                variant="outline"
                onClick={() => {
                  setStatutFilter('');
                  setMatiereFilter('');
                  setClasseFilter('');
                  setSearchTerm('');
                }}
              >
                Réinitialiser
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white p-4 rounded-lg shadow-md">
        <StandardTable
          title="Liste des enseignants"
          columns={standardColumns}
          data={paginatedEnseignants}
          actions={tableActions}
          loading={loading}
          emptyMessage={searchTerm ? "Aucun enseignant ne correspond à votre recherche." : "Aucun enseignant enregistré."}
          emptyIcon={<Users />}
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          showPagination={true}
        />
      </div>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={enseignantToEdit ? "Modification de l'enseignant" : "Ajout d'un enseignant"}
      >
        <EnseignantForm
          initialEnseignant={enseignantToEdit}
          onSuccess={() => {
            console.log("🚨 onSuccess appelé - FERMETURE DU MODAL");
            console.trace("Stack trace pour voir d'où vient l'appel");
            setShowModal(false);
            fetchEnseignants();
          }}
        />
      </Modal>

      {/* Modal de détails */}
      {enseignantToView && (
        <EnseignantDetailModal
          enseignant={enseignantToView}
          isOpen={showDetailModal}
          onClose={handleCloseDetailModal}
        />
      )}

      {/* Modal de confirmation de suppression */}
      <ConfirmationComponent />
    </div>
  );
}