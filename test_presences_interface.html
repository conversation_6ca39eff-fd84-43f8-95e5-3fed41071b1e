<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interface Présences</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Test Interface Présences</h1>
        
        <!-- Test API Backend -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Test API Backend</h2>
            
            <div class="space-y-4">
                <button 
                    onclick="testAbsencesAPI()" 
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                    Tester API Absences
                </button>
                
                <button 
                    onclick="testStatistiquesAPI()" 
                    class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                    Tester API Statistiques
                </button>
                
                <button 
                    onclick="testElevesAPI()" 
                    class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                >
                    Tester API Élèves
                </button>
            </div>
            
            <div id="api-results" class="mt-4 p-4 bg-gray-50 rounded border min-h-[200px]">
                <p class="text-gray-500">Cliquez sur un bouton pour tester l'API...</p>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-blue-800 mb-2">Instructions de Test</h3>
            <div class="text-sm text-blue-700">
                <ol class="list-decimal pl-5 space-y-1">
                    <li>Testez d'abord les API ci-dessus</li>
                    <li>Si les API fonctionnent, allez sur <code>/admin/presences</code></li>
                    <li>Vérifiez que la page se charge sans erreur</li>
                    <li>Testez le bouton "Nouvelle Absence"</li>
                    <li>Vérifiez que le formulaire s'ouvre correctement</li>
                </ol>
            </div>
        </div>

        <!-- Simulation du Formulaire -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Aperçu du Formulaire d'Absence</h2>
            
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Élève</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option>Sélectionner un élève</option>
                            <option>Ahmed Ben Ali - 12345</option>
                            <option>Fatima El Mansouri - 67890</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Classe</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option>Sélectionner une classe</option>
                            <option>6ème A</option>
                            <option>5ème B</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date de début</label>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="date" class="px-3 py-2 border border-gray-300 rounded-md">
                            <input type="time" class="px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date de fin</label>
                        <div class="grid grid-cols-2 gap-2">
                            <input type="date" class="px-3 py-2 border border-gray-300 rounded-md">
                            <input type="time" class="px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Motif</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option>Maladie</option>
                            <option>Raison familiale</option>
                            <option>Autre</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center space-x-3 pt-6">
                        <input type="checkbox" class="h-4 w-4 text-blue-600">
                        <label class="text-sm font-medium text-gray-700">Absence justifiée</label>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Commentaire</label>
                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Détails supplémentaires..."></textarea>
                </div>

                <div class="flex justify-end space-x-3 pt-4 border-t">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Créer l'absence
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testAbsencesAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p class="text-blue-600">Test en cours...</p>';
            
            try {
                const response = await fetch('/api/absences-eleves');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h4 class="font-semibold text-green-600 mb-2">✅ API Absences - Succès</h4>
                    <pre class="text-sm bg-white p-2 rounded border overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h4 class="font-semibold text-red-600 mb-2">❌ API Absences - Erreur</h4>
                    <p class="text-red-700">${error.message}</p>
                `;
            }
        }

        async function testStatistiquesAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p class="text-blue-600">Test en cours...</p>';
            
            try {
                const response = await fetch('/api/absences-eleves/statistiques');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h4 class="font-semibold text-green-600 mb-2">✅ API Statistiques - Succès</h4>
                    <pre class="text-sm bg-white p-2 rounded border overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h4 class="font-semibold text-red-600 mb-2">❌ API Statistiques - Erreur</h4>
                    <p class="text-red-700">${error.message}</p>
                `;
            }
        }

        async function testElevesAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p class="text-blue-600">Test en cours...</p>';
            
            try {
                const response = await fetch('/api/eleves');
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h4 class="font-semibold text-green-600 mb-2">✅ API Élèves - Succès</h4>
                    <pre class="text-sm bg-white p-2 rounded border overflow-auto">${JSON.stringify(data, null, 2).substring(0, 1000)}...</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h4 class="font-semibold text-red-600 mb-2">❌ API Élèves - Erreur</h4>
                    <p class="text-red-700">${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
