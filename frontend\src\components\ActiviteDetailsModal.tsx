import React, { useState, useEffect } from 'react'; 
import { X, Users, Plus, Trash2, Calendar, Clock, MapPin, DollarSign, User, Search } from 'lucide-react';
import Button from './Button';

import { 
  getActiviteEleves, 
  getEleves, 
  inscrireEleveActivite, 
  desinscrireEleveActivite 
} from '../services/api';
import { Activite, Eleve } from '../types';
import { useDeleteConfirmation } from '../hooks/useConfirmation';

interface ActiviteDetailsModalProps {
  activite: Activite;
  onClose: () => void;
  onRefresh: () => void;
}

const ActiviteDetailsModal: React.FC<ActiviteDetailsModalProps> = ({ 
  activite, 
  onClose, 
  onRefresh 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [elevesInscrits, setElevesInscrits] = useState<any[]>([]);
  const [tousEleves, setTousEleves] = useState<Eleve[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  useEffect(() => {
    fetchElevesInscrits();
    fetchTousEleves();
  }, [activite.id_activite]);

  const fetchElevesInscrits = async () => {
    try {
      setLoading(true);
      const response = await getActiviteEleves(activite.id_activite);
      setElevesInscrits(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves inscrits:', error);
      setError('Erreur lors du chargement des élèves inscrits');
    } finally {
      setLoading(false);
    }
  };

  const fetchTousEleves = async () => {
    try {
      const response = await getEleves();
      setTousEleves(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves:', error);
    }
  };

  const handleInscrireEleve = async (idEleve: number) => {
    try {
      setError(null);
      await inscrireEleveActivite(activite.id_activite, idEleve);
      setSuccessMessage('Élève inscrit avec succès');
      await fetchElevesInscrits();
      onRefresh();
      
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      console.error('Erreur lors de l\'inscription:', error);
      setError(error.response?.data?.message || 'Erreur lors de l\'inscription de l\'élève');
    }
  };

  const handleDesinscrireEleve = (idEleve: number, nomEleve: string) => {
    confirmDelete(
      () => desinscrireEleve(idEleve),
      `l'inscription de ${nomEleve}`,
      `Êtes-vous sûr de vouloir désinscrire ${nomEleve} de cette activité ?`
    );
  };

  const desinscrireEleve = async (idEleve: number) => {
    try {
      setError(null);
      await desinscrireEleveActivite(activite.id_activite, idEleve);
      setSuccessMessage('Élève désinscrit avec succès');
      await fetchElevesInscrits();
      onRefresh();
      
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      console.error('Erreur lors de la désinscription:', error);
      setError(error.response?.data?.message || 'Erreur lors de la désinscription de l\'élève');
    }
  };

  const getElevesNonInscrits = () => {
    const idsInscrits = elevesInscrits.map(e => e.id_eleve);
    return tousEleves.filter(eleve => !idsInscrits.includes(eleve.id_eleve));
  };

  const filteredElevesNonInscrits = getElevesNonInscrits().filter(eleve => {
    const nomComplet = eleve.user ? `${eleve.user.nom} ${eleve.user.prenom}` : '';
    return nomComplet.toLowerCase().includes(searchTerm.toLowerCase()) ||
           eleve.code_massar?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatType = (type: string) => {
    const types: { [key: string]: string } = {
      'sportive': 'Sportive',
      'culturelle': 'Culturelle',
      'autre': 'Autre'
    };
    return types[type] || type;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sportive': return 'bg-green-100 text-green-800';
      case 'culturelle': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-6xl max-h-[95vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {activite.nom_activite}
              </h2>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${getTypeColor(activite.type_activite)}`}>
                {formatType(activite.type_activite)}
              </span>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          <div className="p-6 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
            {successMessage && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {successMessage}
              </div>
            )}
            {/* Reste du contenu */}
          </div>
        </div>
      </div>

      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[95vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Inscrire un élève
              </h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              {/* Contenu de recherche et liste */}
            </div>
          </div>
        </div>
      )}

      <ConfirmationComponent />
    </>
  );
};

export default ActiviteDetailsModal;
