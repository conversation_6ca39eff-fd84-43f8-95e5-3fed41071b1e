import React, { useState, useEffect } from 'react';
import { X, Users, Plus, Trash2, Calendar, Clock, MapPin, DollarSign, User, Search } from 'lucide-react';
import Button from './Button';

import { 
  getActiviteEleves, 
  getEleves, 
  inscrireEleveActivite, 
  desinscrireEleveActivite 
} from '../services/api';
import { Activite, Eleve } from '../types';
import { useDeleteConfirmation } from '../hooks/useConfirmation';

interface ActiviteDetailsModalProps {
  activite: Activite;
  onClose: () => void;
  onRefresh: () => void;
}

const ActiviteDetailsModal: React.FC<ActiviteDetailsModalProps> = ({ 
  activite, 
  onClose, 
  onRefresh 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [elevesInscrits, setElevesInscrits] = useState<any[]>([]);
  const [tousEleves, setTousEleves] = useState<Eleve[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  useEffect(() => {
    fetchElevesInscrits();
    fetchTousEleves();
  }, [activite.id_activite]);

  const fetchElevesInscrits = async () => {
    try {
      setLoading(true);
      const response = await getActiviteEleves(activite.id_activite);
      setElevesInscrits(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves inscrits:', error);
      setError('Erreur lors du chargement des élèves inscrits');
    } finally {
      setLoading(false);
    }
  };

  const fetchTousEleves = async () => {
    try {
      const response = await getEleves();
      setTousEleves(response.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des élèves:', error);
    }
  };

  const handleInscrireEleve = async (idEleve: number) => {
    try {
      setError(null);
      await inscrireEleveActivite(activite.id_activite, idEleve);
      setSuccessMessage('Élève inscrit avec succès');
      await fetchElevesInscrits();
      onRefresh();
      
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      console.error('Erreur lors de l\'inscription:', error);
      setError(error.response?.data?.message || 'Erreur lors de l\'inscription de l\'élève');
    }
  };

  const handleDesinscrireEleve = (idEleve: number, nomEleve: string) => {
    confirmDelete(
      () => desinscrireEleve(idEleve),
      `l'inscription de ${nomEleve}`,
      `Êtes-vous sûr de vouloir désinscrire ${nomEleve} de cette activité ?`
    );
  };

  const desinscrireEleve = async (idEleve: number) => {
    try {
      setError(null);
      await desinscrireEleveActivite(activite.id_activite, idEleve);
      setSuccessMessage('Élève désinscrit avec succès');
      await fetchElevesInscrits();
      onRefresh();
      
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      console.error('Erreur lors de la désinscription:', error);
      setError(error.response?.data?.message || 'Erreur lors de la désinscription de l\'élève');
    }
  };

  const getElevesNonInscrits = () => {
    const idsInscrits = elevesInscrits.map(e => e.id_eleve);
    return tousEleves.filter(eleve => !idsInscrits.includes(eleve.id_eleve));
  };

  const filteredElevesNonInscrits = getElevesNonInscrits().filter(eleve => {
    const nomComplet = eleve.user ? `${eleve.user.nom} ${eleve.user.prenom}` : '';
    return nomComplet.toLowerCase().includes(searchTerm.toLowerCase()) ||
           eleve.code_massar?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatType = (type: string) => {
    const types: { [key: string]: string } = {
      'sportive': 'Sportive',
      'culturelle': 'Culturelle',
      'autre': 'Autre'
    };
    return types[type] || type;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'sportive': return 'bg-green-100 text-green-800';
      case 'culturelle': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-6xl max-h-[95vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {activite.nom_activite}
              </h2>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${getTypeColor(activite.type_activite)}`}>
                {formatType(activite.type_activite)}
              </span>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          <div className="p-6 space-y-6">
          {/* Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {successMessage}
            </div>
          )}

          {/* Informations de l'activité */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Informations générales</h3>
              
              {activite.description && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Description</p>
                  <p className="text-sm text-gray-900">{activite.description}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Date de début</p>
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                    {formatDate(activite.date_debut)}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date de fin</p>
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                    {formatDate(activite.date_fin)}
                  </div>
                </div>
              </div>

              {activite.jour_semaine && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Horaires</p>
                    <div className="flex items-center text-sm text-gray-900">
                      <Clock className="w-4 h-4 text-gray-400 mr-1" />
                      {activite.heure_debut} - {activite.heure_fin}
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Jour</p>
                    <p className="text-sm text-gray-900">{activite.jour_semaine}</p>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                {activite.lieu && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Lieu</p>
                    <div className="flex items-center text-sm text-gray-900">
                      <MapPin className="w-4 h-4 text-gray-400 mr-1" />
                      {activite.lieu}
                    </div>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-gray-500">Prix</p>
                  <div className="flex items-center text-sm text-gray-900">
                    <DollarSign className="w-4 h-4 text-gray-400 mr-1" />
                    {activite.prix} DH
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Statistiques</h3>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Users className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-gray-700">Participants</span>
                  </div>
                  <span className="text-lg font-bold text-gray-900">
                    {elevesInscrits.length}/{activite.capacite_max || 50}
                  </span>
                </div>
                <div className="mt-2 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${Math.min((elevesInscrits.length / (activite.capacite_max || 50)) * 100, 100)}%` 
                    }}
                  ></div>
                </div>
              </div>

              {activite.responsable && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Responsable</p>
                  <div className="flex items-center text-sm text-gray-900">
                    <User className="w-4 h-4 text-gray-400 mr-1" />
                    {activite.responsable.prenom} {activite.responsable.nom}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Liste des élèves inscrits */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Élèves inscrits ({elevesInscrits.length})
              </h3>
              <Button
                icon={<Plus size={16} />}
                variant="primary"
                size="sm"
                onClick={() => setShowAddModal(true)}
                disabled={elevesInscrits.length >= (activite.capacite_max || 50)}
              >
                Inscrire un élève
              </Button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : elevesInscrits.length > 0 ? (
              <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Élève
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Classe
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date d'inscription
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {elevesInscrits.map((eleve) => (
                      <tr key={eleve.id_eleve} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {eleve.prenom} {eleve.nom}
                            </div>
                            <div className="text-sm text-gray-500">
                              {eleve.code_massar}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {eleve.nom_classe}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(eleve.date_inscription)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleDesinscrireEleve(eleve.id_eleve, `${eleve.prenom} ${eleve.nom}`)}
                            className="text-red-600 hover:text-red-900"
                            title="Désinscrire"
                          >
                            <Trash2 size={16} />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun élève inscrit</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Commencez par inscrire des élèves à cette activité.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal d'ajout d'élève */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[95vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Inscrire un élève
              </h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Rechercher un élève..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {filteredElevesNonInscrits.length > 0 ? (
                <div className="space-y-2">
                  {filteredElevesNonInscrits.map((eleve) => (
                    <div
                      key={eleve.id_eleve}
                      className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {eleve.user ? `${eleve.user.prenom} ${eleve.user.nom}` : 'Nom non disponible'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {eleve.code_massar} • {eleve.nom_classe}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="primary"
                        onClick={() => {
                          handleInscrireEleve(eleve.id_eleve);
                          setShowAddModal(false);
                        }}
                      >
                        Inscrire
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {searchTerm ? 'Aucun élève trouvé' : 'Tous les élèves sont déjà inscrits'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm ? 'Essayez avec d\'autres termes de recherche.' : 'Cette activité est complète.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        </div>
      )}

      <ConfirmationComponent />
    </>
  );
};

export default ActiviteDetailsModal;
