import React, { useState, useEffect } from 'react';
import { Plus, Search, Users, GraduationCap, Edit, Trash2, Eye, Filter } from 'lucide-react';
import Button from '../../components/Button';
import Input from '../../components/Input';
import Select from '../../components/Select';
import Table from '../../components/Table';
import { Classe, NiveauDetaille } from '../../types';
import { getClasses, getNiveaux, addClasse, updateClasse, deleteClasse, getClasseEleves, getClassesEnseignant, getEnseignants } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import ClasseModal from '../../components/modals/ClasseModal';
import ElevesClasseModal from '../../components/modals/ElevesClasseModal';
import Pagination from '../../components/Pagination';
import { usePagination } from '../../hooks/usePagination';

const Classes: React.FC = () => {
  const { user } = useAuth();
  const [classes, setClasses] = useState<Classe[]>([]);
  const [niveaux, setNiveaux] = useState<NiveauDetaille[]>([]);
  const [filteredClasses, setFilteredClasses] = useState<Classe[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCycle, setSelectedCycle] = useState('');
  const [selectedNiveau, setSelectedNiveau] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showElevesModal, setShowElevesModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [editingClasse, setEditingClasse] = useState<Classe | null>(null);
  const [selectedClasse, setSelectedClasse] = useState<Classe | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Debug: Afficher les informations de l'utilisateur
  console.log("🔍 DEBUG Classes - Utilisateur connecté:", user);
  console.log("🔍 DEBUG Classes - Rôle utilisateur:", user?.role);

  const cycleOptions = [
    { value: '', label: 'Tous les cycles' },
    { value: 'maternelle', label: 'Maternelle' },
    { value: 'primaire', label: 'Primaire' },
    { value: 'collège', label: 'Collège' },
    { value: 'lycée', label: 'Lycée' }
  ];

  const cycleColors = {
    maternelle: 'bg-pink-100 text-pink-800',
    primaire: 'bg-blue-100 text-blue-800',
    collège: 'bg-green-100 text-green-800',
    lycée: 'bg-purple-100 text-purple-800'
  };

  useEffect(() => {
    loadClasses();
    loadNiveaux();
  }, []);

  useEffect(() => {
    filterClasses();
  }, [classes, searchTerm, selectedCycle, selectedNiveau]);

  const loadClasses = async () => {
    setIsLoading(true);
    try {
      console.log("🔍 Chargement des classes - Rôle:", user?.role);

      if (user?.role === 'enseignant') {
        // Pour les enseignants, récupérer seulement leurs classes
        console.log("🧑‍🏫 Enseignant - Récupération de ses classes");

        // D'abord récupérer l'enseignant connecté
        const enseignantsRes = await getEnseignants();
        const enseignantsData = enseignantsRes.data?.data || [];
        const enseignantConnecte = enseignantsData.find((ens: any) => ens.id_utilisateur === user.id_utilisateur);

        if (enseignantConnecte) {
          console.log("📚 Récupération des classes de l'enseignant ID:", enseignantConnecte.id_enseignant);
          const response = await getClassesEnseignant(enseignantConnecte.id_enseignant);
          if (response.data.success) {
            console.log("✅ Classes de l'enseignant:", response.data.data);
            setClasses(response.data.data);
          } else {
            setMessage({ type: 'error', text: 'Erreur lors du chargement des classes' });
          }
        } else {
          console.warn("⚠️ Enseignant connecté non trouvé");
          setClasses([]);
        }
      } else {
        // Pour les admins, récupérer toutes les classes
        console.log("👑 Admin - Récupération de toutes les classes");
        const response = await getClasses();
        if (response.data.success) {
          setClasses(response.data.data);
        } else {
          setMessage({ type: 'error', text: 'Erreur lors du chargement des classes' });
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des classes:', error);
      setMessage({ type: 'error', text: 'Erreur lors du chargement des classes' });
    } finally {
      setIsLoading(false);
    }
  };

  const loadNiveaux = async () => {
    try {
      const response = await getNiveaux();
      if (response.data.success) {
        setNiveaux(response.data.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des niveaux:', error);
    }
  };

  const filterClasses = () => {
    let filtered = classes;

    if (searchTerm) {
      filtered = filtered.filter(classe =>
        classe.nom_classe.toLowerCase().includes(searchTerm.toLowerCase()) ||
        classe.niveau_libelle?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedCycle) {
      filtered = filtered.filter(classe => classe.cycle === selectedCycle);
    }

    if (selectedNiveau) {
      filtered = filtered.filter(classe => classe.id_niveau === parseInt(selectedNiveau));
    }

    setFilteredClasses(filtered);
    resetPagination(); // Réinitialiser la pagination lors du filtrage
  };

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedClasses,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredClasses,
    initialItemsPerPage: 15
  });

  // Configuration des colonnes pour le tableau (format Élèves)
  const columns = [
    { header: "Classe", accessor: "nom_classe", className: "p-3" },
    { header: "Niveau", accessor: "niveau", className: "p-3" },
    { header: "Cycle", accessor: "cycle", className: "p-3" },
    { header: "Élèves", accessor: "eleves", className: "p-3" },
    ...(user?.role !== 'enseignant' ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
  ];



  const renderRow = (classe: Classe) => (
    <tr key={classe.id_classe} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3">
        <div className="flex items-center">
          <Users className="h-4 w-4 text-gray-400 mr-3" />
          <div className="text-sm font-medium text-gray-900">
            {classe.nom_classe}
          </div>
        </div>
      </td>
      <td className="p-3">
        <div className="text-sm text-gray-900">{classe.niveau_libelle}</div>
      </td>
      <td className="p-3">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${cycleColors[classe.cycle as keyof typeof cycleColors] || 'bg-gray-100 text-gray-800'}`}>
          {classe.cycle}
        </span>
      </td>
      <td className="p-3">
        <div className="text-sm text-gray-900">
          {classe.nombre_eleves || 0} élève{(classe.nombre_eleves || 0) > 1 ? 's' : ''}
        </div>
      </td>
      {user?.role !== 'enseignant' && (
        <td className="p-3">
          <div className="flex space-x-2">
            <button
              className="text-blue-600 hover:text-blue-900"
              onClick={() => handleViewEleves(classe)}
              title="Voir les élèves"
            >
              <Eye size={16} />
            </button>
            <button
              className="text-indigo-600 hover:text-indigo-900"
              onClick={() => handleEdit(classe)}
              title="Modifier"
            >
              <Edit size={16} />
            </button>
            <button
              className="text-red-600 hover:text-red-900"
              onClick={() => handleDelete(classe.id_classe)}
              title="Supprimer"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </td>
      )}
    </tr>
  );

  const handleAdd = () => {
    setEditingClasse(null);
    setShowModal(true);
  };

  const handleEdit = (classe: Classe) => {
    setEditingClasse(classe);
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette classe ?')) {
      try {
        const response = await deleteClasse(id);
        if (response.data.success) {
          setMessage({ type: 'success', text: 'Classe supprimée avec succès' });
          loadClasses();
        } else {
          setMessage({ type: 'error', text: response.data.message || 'Erreur lors de la suppression' });
        }
        setTimeout(() => setMessage(null), 3000);
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        setMessage({ type: 'error', text: 'Erreur lors de la suppression' });
      }
    }
  };

  const handleViewEleves = (classe: Classe) => {
    setSelectedClasse(classe);
    setShowElevesModal(true);
  };

  const handleSave = async (data: any) => {
    try {
      let response;
      if (editingClasse) {
        response = await updateClasse(editingClasse.id_classe, data);
      } else {
        response = await addClasse(data);
      }

      if (response.data.success) {
        setMessage({ 
          type: 'success', 
          text: editingClasse ? 'Classe mise à jour avec succès' : 'Classe créée avec succès' 
        });
        loadClasses();
        setShowModal(false);
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Erreur lors de la sauvegarde' });
      }
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setMessage({ type: 'error', text: 'Erreur lors de la sauvegarde' });
    }
  };

  const getNiveauOptions = () => {
    const options = [{ value: '', label: 'Tous les niveaux' }];
    niveaux.forEach(niveau => {
      options.push({
        value: niveau.id_niveau.toString(),
        label: `${niveau.libelle} (${niveau.cycle})`
      });
    });
    return options;
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Message de statut */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {/* Header avec recherche et boutons */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher une classe..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        <div className="flex space-x-2">
          <Button
            icon={<Filter size={16} />}
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filtres
          </Button>
          {user?.role !== 'enseignant' && (
            <Button
              icon={<Plus size={16} />}
              variant="primary"
              onClick={handleAdd}
            >
              Ajouter une classe
            </Button>
          )}
        </div>
      </div>

      {/* Filtres dépliables */}
      {showFilters && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select
              name="cycle"
              label="Cycle"
              value={selectedCycle}
              onChange={(e) => setSelectedCycle(e.target.value)}
              options={cycleOptions}
            />

            <Select
              name="niveau"
              label="Niveau"
              value={selectedNiveau}
              onChange={(e) => setSelectedNiveau(e.target.value)}
              options={getNiveauOptions()}
            />

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCycle('');
                  setSelectedNiveau('');
                }}
                className="w-full"
              >
                Réinitialiser
              </Button>
            </div>
          </div>
        </div>
      )}



      {/* Tableau des classes */}
      <div className="">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : paginatedClasses.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Users className="mx-auto mb-2 w-10 h-10" />
            <p>Aucune classe trouvée</p>
            {searchTerm && (
              <p className="text-sm">
                Essayez de modifier votre recherche ou{" "}
                <button onClick={() => setSearchTerm('')} className="text-blue-600 underline">
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <Table columns={columns} data={paginatedClasses} renderRow={renderRow} />

        {/* Pagination */}
        {filteredClasses.length > 0 && (
          <div className="px-6 py-3 border-t border-gray-200">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              showItemsPerPage={true}
            />
          </div>
        )}
        </div>
        )}
      </div>

      {/* Modals */}
      {showModal && (
        <ClasseModal
          classe={editingClasse}
          niveaux={niveaux}
          onSave={handleSave}
          onClose={() => setShowModal(false)}
        />
      )}

      {showElevesModal && selectedClasse && (
        <ElevesClasseModal
          classe={selectedClasse}
          onClose={() => setShowElevesModal(false)}
        />
      )}
    </div>
  );
};

export default Classes;
