import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Login from "./pages/Login";
import ChangePassword from "./pages/ChangePassword";
import Dashboard from "./pages/admin/Dashboard";
import DashboardEnseignant from "./pages/enseignant/DashboardEnseignant";
import DashboardEleve from "./pages/eleve/DashboardEleve";
import DashboardParent from "./pages/parent/DashboardParent";
import Eleves from "./pages/admin/Eleves";
import Enseignants from "./pages/admin/Enseignants";
import Parents from "./pages/admin/Parents";
import Classes from "./pages/admin/Classes";
import SchoolSettings from "./pages/admin/SchoolSettings";
import Layout from "./pages/Layout";
import { useAuth } from "./context/AuthContext";
import Salles from "./pages/admin/Salles";
import Matieres from "./pages/admin/Matieres";
import EmploiDuTemps from "./pages/admin/EmploiDuTemps";
import EmploiDuTempsEleve from "./pages/eleve/EmploiDuTemps";
import Paiements from "./pages/admin/Paiements";
import Examens from "./pages/admin/Examens";
import Bulletins from "./pages/admin/Bulletins";
import Activites from "./pages/admin/Activites";
// import { paiementScheduler } from "./services/paiementScheduler";

export default function App() {
  const { user } = useAuth(); // Récupérer le rôle de l'utilisateur

  return (
    <Router>
      <Routes>
        {/* Routes publiques */}
        <Route path="/login" element={<Login />} />
        <Route path="/change-password" element={<ChangePassword />} />

        {/* Routes protégées */}
        {user?.role === "admin" && (
          <Route path="/admin/*" element={<Layout />}>
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="school-settings" element={<SchoolSettings />} />
            <Route path="eleves" element={<Eleves />} />
            <Route path="enseignants" element={<Enseignants />} />
            <Route path="parents" element={<Parents />} />
            <Route path="classes" element={<Classes />} />
            <Route path="salles" element={<Salles />} />
            <Route path="matieres" element={<Matieres />} />
            <Route path="emploi-du-temps" element={<EmploiDuTemps />} />
            <Route path="paiements" element={<Paiements />} />
            <Route path="examens" element={<Examens />} />
            <Route path="bulletins" element={<Bulletins />} />
            <Route path="activites" element={<Activites />} />
            {/* <Route path="transport" element={<Transport />} />
            <Route path="presences" element={<Presences />} /> */}
          </Route>
        )}

        {user?.role === "enseignant" && (
          <Route path="/enseignant/*" element={<Layout />} >
            <Route path="dashboard" element={<DashboardEnseignant />} />
            <Route path="eleves" element={<Eleves />} />
            <Route path="classes" element={<Classes />} />
            <Route path="emploi-du-temps" element={<EmploiDuTemps />} />
          </Route>
        )}

        {user?.role === "eleve" && (
          <Route path="/eleve/*" element={<Layout />} >
            <Route path="dashboard" element={<DashboardEleve />} />
            <Route path="emploi-du-temps" element={<EmploiDuTempsEleve />} />
          </Route>
        )}

        {user?.role === "parent" && (
          <Route path="/parent/*" element={<Layout />} >
            <Route path="dashboard" element={<DashboardParent />} />
          </Route>   
        )}

        {/* Redirection par défaut */}
        <Route path="*" element={<Login/>} />

      </Routes>
    </Router>
  );
}