<?php
// routes/transport.php

require_once __DIR__ . '/../controllers/TransportController.php';

function handleTransportRoutes($uri, $method, $pdo) {
    $controller = new TransportController();
    
    // GET /transports/statistiques - Récupérer les statistiques
    if ($uri === '/transports/statistiques' && $method === 'GET') {
        $controller->getStatistiques();
        return true;
    }
    
    // GET /transports - Récupérer tous les transports
    if ($uri === '/transports' && $method === 'GET') {
        $controller->index();
        return true;
    }
    
    // POST /transports - Créer un nouveau transport
    if ($uri === '/transports' && $method === 'POST') {
        $controller->store();
        return true;
    }
    
    // GET /transports/{id} - Récupérer un transport par ID
    if (preg_match('/^\/transports\/(\d+)$/', $uri, $matches) && $method === 'GET') {
        $id = $matches[1];
        $controller->show($id);
        return true;
    }
    
    // PUT /transports/{id} - Mettre à jour un transport
    if (preg_match('/^\/transports\/(\d+)$/', $uri, $matches) && $method === 'PUT') {
        $id = $matches[1];
        $controller->update($id);
        return true;
    }
    
    // DELETE /transports/{id} - Supprimer un transport
    if (preg_match('/^\/transports\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = $matches[1];
        $controller->destroy($id);
        return true;
    }
    
    // GET /transports/{id}/eleves - Récupérer les élèves inscrits à un transport
    if (preg_match('/^\/transports\/(\d+)\/eleves$/', $uri, $matches) && $method === 'GET') {
        $id = $matches[1];
        $controller->getEleves($id);
        return true;
    }
    
    // POST /transports/{id}/eleves/{id_eleve} - Inscrire un élève à un transport
    if (preg_match('/^\/transports\/(\d+)\/eleves\/(\d+)$/', $uri, $matches) && $method === 'POST') {
        $id = $matches[1];
        $id_eleve = $matches[2];
        $controller->inscrireEleve($id, $id_eleve);
        return true;
    }
    
    // DELETE /transports/{id}/eleves/{id_eleve} - Désinscrire un élève d'un transport
    if (preg_match('/^\/transports\/(\d+)\/eleves\/(\d+)$/', $uri, $matches) && $method === 'DELETE') {
        $id = $matches[1];
        $id_eleve = $matches[2];
        $controller->desinscrireEleve($id, $id_eleve);
        return true;
    }
    
    return false;
}
?>
