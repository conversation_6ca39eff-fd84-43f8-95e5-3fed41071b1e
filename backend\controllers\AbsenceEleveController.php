<?php
// controllers/AbsenceEleveController.php
require_once __DIR__ . '/../models/AbsenceEleve.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class AbsenceEleveController {
    private $absenceEleve;
    private $pdo;

    public function __construct() {
        $database = new Database();
        $this->pdo = $database->getConnection();
        $this->absenceEleve = new AbsenceEleve($this->pdo);
    }

    // GET /absences-eleves - Récupérer toutes les absences avec filtres
    public function index() {
        try {
            $filters = [];
            
            // Récupérer les filtres depuis les paramètres GET
            if (isset($_GET['date_debut'])) {
                $filters['date_debut'] = $_GET['date_debut'];
            }
            if (isset($_GET['date_fin'])) {
                $filters['date_fin'] = $_GET['date_fin'];
            }
            if (isset($_GET['id_classe'])) {
                $filters['id_classe'] = $_GET['id_classe'];
            }
            if (isset($_GET['motif'])) {
                $filters['motif'] = $_GET['motif'];
            }
            if (isset($_GET['justifiee'])) {
                $filters['justifiee'] = $_GET['justifiee'] === 'true';
            }

            $absences = $this->absenceEleve->getAll($filters);
            
            if ($absences === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des absences'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $absences
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /absences-eleves/{id} - Récupérer une absence par ID
    public function show($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID absence invalide'
                ]);
                return;
            }

            $absence = $this->absenceEleve->getById($id);
            
            if (!$absence) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Absence non trouvée'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $absence
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // POST /absences-eleves - Créer une nouvelle absence
    public function store() {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            $errors = $this->validateAbsenceData($data);
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ]);
                return;
            }

            // Vérifier les conflits de dates
            if ($this->absenceEleve->hasAbsenceAtDate($data['id_eleve'], $data['date_debut'], $data['date_fin'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Cet élève a déjà une absence enregistrée sur cette période'
                ]);
                return;
            }

            $result = $this->absenceEleve->create($data);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création de l\'absence'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Absence créée avec succès',
                'data' => ['id' => $result]
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // PUT /absences-eleves/{id} - Mettre à jour une absence
    public function update($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID absence invalide'
                ]);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            $errors = $this->validateAbsenceData($data);
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ]);
                return;
            }

            // Vérifier les conflits de dates (en excluant l'absence actuelle)
            if ($this->absenceEleve->hasAbsenceAtDate($data['id_eleve'], $data['date_debut'], $data['date_fin'], $id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Cet élève a déjà une absence enregistrée sur cette période'
                ]);
                return;
            }

            $result = $this->absenceEleve->update($id, $data);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour de l\'absence'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Absence mise à jour avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // DELETE /absences-eleves/{id} - Supprimer une absence
    public function destroy($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID absence invalide'
                ]);
                return;
            }

            $result = $this->absenceEleve->delete($id);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression de l\'absence'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Absence supprimée avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /absences-eleves/eleve/{id} - Récupérer les absences d'un élève
    public function getByEleve($id_eleve) {
        try {
            if (!$id_eleve || !is_numeric($id_eleve)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID élève invalide'
                ]);
                return;
            }

            $date_debut = $_GET['date_debut'] ?? null;
            $date_fin = $_GET['date_fin'] ?? null;

            $absences = $this->absenceEleve->getByEleve($id_eleve, $date_debut, $date_fin);
            
            if ($absences === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des absences'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $absences
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /absences-eleves/classe/{id} - Récupérer les absences d'une classe
    public function getByClasse($id_classe) {
        try {
            if (!$id_classe || !is_numeric($id_classe)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID classe invalide'
                ]);
                return;
            }

            $date_debut = $_GET['date_debut'] ?? null;
            $date_fin = $_GET['date_fin'] ?? null;

            $absences = $this->absenceEleve->getByClasse($id_classe, $date_debut, $date_fin);
            
            if ($absences === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des absences'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $absences
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /absences-eleves/statistiques - Récupérer les statistiques
    public function getStatistiques() {
        try {
            $date_debut = $_GET['date_debut'] ?? null;
            $date_fin = $_GET['date_fin'] ?? null;
            $id_classe = $_GET['id_classe'] ?? null;

            $stats = $this->absenceEleve->getStatistiques($date_debut, $date_fin, $id_classe);
            
            if ($stats === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des statistiques'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Validation des données d'absence
    private function validateAbsenceData($data) {
        $errors = [];

        if (empty($data['id_eleve']) || !is_numeric($data['id_eleve'])) {
            $errors[] = 'ID élève invalide';
        }

        if (empty($data['id_classe']) || !is_numeric($data['id_classe'])) {
            $errors[] = 'ID classe invalide';
        }

        if (empty($data['date_debut'])) {
            $errors[] = 'Date de début requise';
        }

        if (empty($data['date_fin'])) {
            $errors[] = 'Date de fin requise';
        }

        if (!empty($data['date_debut']) && !empty($data['date_fin'])) {
            $debut = new DateTime($data['date_debut']);
            $fin = new DateTime($data['date_fin']);
            
            if ($debut > $fin) {
                $errors[] = 'La date de début doit être antérieure à la date de fin';
            }
        }

        if (empty($data['motif']) || !in_array($data['motif'], ['maladie', 'familial', 'autre'])) {
            $errors[] = 'Motif invalide';
        }

        if (!isset($data['justifiee']) || !is_bool($data['justifiee'])) {
            $errors[] = 'Statut de justification invalide';
        }

        return $errors;
    }
}
?>
