<?php

require_once __DIR__ . '/../models/Eleve.php';
require_once __DIR__ . '/../models/AncienneEcole.php';
require_once __DIR__ . '/../models/Inscription.php';
require_once __DIR__ . '/../models/AnneeScolaire.php';

class EleveController {
    private $eleve;
    private $ancienneEcole;
    private $inscription;
    private $anneeScolaire;

    public function __construct($db) {
        $this->eleve = new Eleve($db);
        $this->ancienneEcole = new AncienneEcole($db);
        $this->inscription = new Inscription($db);
        $this->anneeScolaire = new AnneeScolaire($db);
    }

    public function getEleves($id_annee = null , $id_niveau = null ,$id_classe = null) {
        try {
            $eleves = $this->eleve->getAll($id_annee,$id_niveau,$id_classe);
            echo json_encode([
                'success' => true,
                'data' => $eleves,
                'message' => 'Élèves récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans getEleves: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => 'Erreur lors de la récupération des élèves'
            ]);
        }
    }

    public function getElevesTransport() {
        try {
            $eleves = $this->eleve->getElevesAvecTransport();
            echo json_encode([
                'success' => true,
                'data' => $eleves,
                'message' => 'Élèves avec transport récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans getElevesTransport: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => 'Erreur lors de la récupération des élèves avec transport'
            ]);
        }
    }

    public function getEleve($id) {
        try {
            $eleve = $this->eleve->getById($id);
            if ($eleve) {
                echo json_encode([
                    'success' => true,
                    'data' => $eleve,
                    'message' => 'Élève récupéré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'data' => null,
                    'message' => 'Élève non trouvé'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans getEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => null,
                'message' => 'Erreur lors de la récupération de l\'élève'
            ]);
        }
    }

    public function addEleve() {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            // Gérer l'ancienne école si elle est fournie
            if (!empty($data['ancienne_ecole']) && !empty($data['ancienne_ecole']['code_gresa'])) {
                $ancienneEcoleData = $data['ancienne_ecole'];

                // Créer ou récupérer l'ancienne école
                $codeGresa = $this->ancienneEcole->create($ancienneEcoleData);
                if (!$codeGresa) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de l\'ancienne école']);
                    return;
                }

                // Ajouter le code GRESA aux données de l'élève
                $data['code_gresa'] = $codeGresa;
            }

            $id_eleve = $this->eleve->create($data);
            if ($id_eleve) {
                // Gérer l'inscription si id_classe est fourni
                if (!empty($data['id_classe'])) {
                    $activeAnneeScolaire = $this->anneeScolaire->getActive();
                    if ($activeAnneeScolaire) {
                        $id_annee_scolaire = $activeAnneeScolaire['id_annee_scolaire'];
                        $id_classe = $data['id_classe'];

                        $inscriptionResult = $this->inscription->create($id_eleve, $id_annee_scolaire, $id_classe);
                        if (!$inscriptionResult) {
                            error_log("Erreur lors de la création de l'inscription pour l'élève $id_eleve dans la classe $id_classe pour l'année $id_annee_scolaire.");
                        }
                    } else {
                        error_log("Aucune année scolaire active trouvée pour l'inscription de l'élève $id_eleve.");
                    }
                }
                echo json_encode(['success' => true, 'id_eleve' => $id_eleve, 'message' => 'Élève créé avec succès.']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création de l\'élève']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans addEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }
    public function updateEleve($id) {
        try {
            $data = json_decode(file_get_contents("php://input"), true);

            // Gérer l'ancienne école si elle est fournie
            if (!empty($data['ancienne_ecole']) && !empty($data['ancienne_ecole']['code_gresa'])) {
                $ancienneEcoleData = $data['ancienne_ecole'];

                // Créer ou mettre à jour l'ancienne école
                $codeGresa = $this->ancienneEcole->create($ancienneEcoleData);
                if (!$codeGresa) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de la gestion de l\'ancienne école']);
                    return;
                }

                $data['code_gresa'] = $codeGresa;
            } else {
                $data['code_gresa'] = null;
            }

            if ($this->eleve->update($id, $data)) {
                // Gérer l'inscription si id_classe est fourni
                if (!empty($data['id_classe'])) {
                    $activeAnneeScolaire = $this->anneeScolaire->getActive();
                    if ($activeAnneeScolaire) {
                        $id_annee_scolaire = $activeAnneeScolaire['id_annee_scolaire'];
                        $new_id_classe = $data['id_classe'];

                        // Vérifier si une inscription existe déjà pour cet élève et cette année scolaire
                        $existingInscription = $this->inscription->getInscriptionByEleveAndAnneeScolaire($id, $id_annee_scolaire);

                        if ($existingInscription) {
                            // Mettre à jour l'inscription existante
                            $old_id_classe = $existingInscription['id_classe'];
                            $inscriptionResult = $this->inscription->update($id, $id_annee_scolaire, $old_id_classe, $new_id_classe);
                            if (!$inscriptionResult) {
                                error_log("Erreur lors de la mise à jour de l'inscription pour l'élève $id dans la classe $new_id_classe pour l'année $id_annee_scolaire.");
                            }
                        } else {
                            // Créer une nouvelle inscription
                            $inscriptionResult = $this->inscription->create($id, $id_annee_scolaire, $new_id_classe);
                            if (!$inscriptionResult) {
                                error_log("Erreur lors de la création de l'inscription pour l'élève $id dans la classe $new_id_classe pour l'année $id_annee_scolaire.");
                            }
                        }
                    } else {
                        error_log("Aucune année scolaire active trouvée pour l'inscription de l'élève $id.");
                    }
                }
                echo json_encode(['success' => true, 'message' => 'Élève mis à jour avec succès']);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la mise à jour de l\'élève']);
            }
        } catch (Exception $e) {
            error_log("Erreur dans updateEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Erreur serveur: ' . $e->getMessage()]);
        }
    }

    public function deleteEleve($id) {
        if ($this->eleve->delete($id)) echo json_encode(['message' => 'Élève supprimé']);
        else http_response_code(400);
    }

    // Récupérer les enfants du parent connecté
    public function getElevesByParent() {
        session_start();
        if (!isset($_SESSION['id_parent'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Non authentifié']);
            return;
        }
        $id_parent = $_SESSION['id_parent'];
        try {
            $eleves = $this->eleve->getByParent($id_parent);
            echo json_encode([
                'success' => true,
                'data' => $eleves,
                'message' => 'Enfants du parent récupérés avec succès'
            ]);
        } catch (Exception $e) {
            error_log("Erreur dans getElevesByParent: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => [],
                'message' => 'Erreur lors de la récupération des enfants'
            ]);
        }
    }

    // Récupérer l'id_eleve à partir de l'id_utilisateur
    public function getIdEleveByUtilisateur($id_utilisateur) {
        try {
            $id_eleve = $this->eleve->getIdEleve($id_utilisateur);
            if ($id_eleve) {
                echo json_encode([
                    'success' => true,
                    'id_eleve' => $id_eleve,
                    'message' => 'id_eleve récupéré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'id_eleve' => null,
                    'message' => 'Aucun élève trouvé pour cet utilisateur'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans getIdEleveByUtilisateur: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'id_eleve' => null,
                'message' => 'Erreur lors de la récupération de l\'id_eleve'
            ]);
        }
    }

    // Récupérer le niveau d'un élève avec les prix
    public function getNiveauEleve($id_eleve) {
        try {
            $niveau = $this->eleve->getNiveauWithPrices($id_eleve);
            if ($niveau) {
                echo json_encode([
                    'success' => true,
                    'data' => $niveau,
                    'message' => 'Niveau de l\'élève récupéré avec succès'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'data' => null,
                    'message' => 'Niveau non trouvé pour cet élève'
                ]);
            }
        } catch (Exception $e) {
            error_log("Erreur dans getNiveauEleve: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'data' => null,
                'message' => 'Erreur lors de la récupération du niveau de l\'élève'
            ]);
        }
    }


}
