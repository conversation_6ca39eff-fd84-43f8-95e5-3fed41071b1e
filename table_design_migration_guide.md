# 📊 Guide de Migration - Design Standard des Tableaux

## 🎯 **Objectif**
Appliquer le même design de tableau et pagination de la page **Élèves** à toutes les pages de l'application.

## 🎨 **Design Standard (Page Élèves)**

### **Structure HTML**
```jsx
{loading ? (
  <div className="flex items-center justify-center py-8">
    <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
    <span className="ml-2">Chargement...</span>
  </div>
) : paginatedData.length === 0 ? (
  <div className="text-center py-8 text-gray-500">
    <IconComponent className="mx-auto mb-2 w-10 h-10" />
    <p>Aucun élément trouvé</p>
    {searchTerm && (
      <p className="text-sm">
        Essayez de modifier votre recherche ou{" "}
        <button onClick={() => setSearchTerm('')} className="text-blue-600 underline">
          effacer les filtres
        </button>
      </p>
    )}
  </div>
) : (
<div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
  <Table columns={columns} data={paginatedData} renderRow={renderRow} />
  {/* Pagination */}
  {filteredData.length > 0 && (
    <div className="px-6 py-3 border-gray-200 text-sm">
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={setItemsPerPage}
        showItemsPerPage={true}
      />
    </div>
  )}
</div>
)}
```

### **Configuration des Colonnes**
```jsx
const columns = [
  { header: "Nom", accessor: "nom", className: "p-3" },
  { header: "Prénom", accessor: "prenom", className: "p-3" },
  { header: "Email", accessor: "email", className: "p-3" },
  { header: "Statut", accessor: "statut", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

### **Fonction renderRow**
```jsx
const renderRow = (item: ItemType) => (
  <tr key={item.id} className="border-b even:bg-gray-50 hover:bg-gray-50">
    <td className="p-3 font-semibold">{item.nom || "-"}</td>
    <td className="p-3">{item.prenom || "-"}</td>
    <td className="p-3">{item.email || "-"}</td>
    <td className="p-3">
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
        item.statut === 'actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {item.statut}
      </span>
    </td>
    {user?.role === "admin" && (
      <td className="p-3">
        <div className="flex space-x-2">
          <button
            onClick={() => handleView(item)}
            className="text-blue-600 hover:text-blue-900"
            title="Voir les détails"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={() => handleEdit(item)}
            className="text-indigo-600 hover:text-indigo-900"
            title="Modifier"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => handleDelete(item.id, item.nom)}
            className="text-red-600 hover:text-red-900"
            title="Supprimer"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </td>
    )}
  </tr>
);
```

## 🔧 **Imports Nécessaires**

```jsx
import Table from "../../components/Table";
import Pagination from "../../components/Pagination";
import { usePagination } from "../../hooks/usePagination";
import { Eye, Edit, Trash2, Users } from "lucide-react";
```

## 📋 **Checklist de Migration**

### **✅ Pages Migrées**
- [x] **Élèves** : Design de référence
- [x] **Enseignants** : Migré avec succès

### **🔄 Pages à Migrer**

#### **1. Parents**
```jsx
// Colonnes spécifiques
const columns = [
  { header: "Nom", accessor: "nom", className: "p-3" },
  { header: "Prénom", accessor: "prenom", className: "p-3" },
  { header: "CIN", accessor: "cin", className: "p-3" },
  { header: "Téléphone", accessor: "telephone", className: "p-3" },
  { header: "Email", accessor: "email", className: "p-3" },
  { header: "Enfants", accessor: "enfants", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

#### **2. Classes**
```jsx
// Colonnes spécifiques
const columns = [
  { header: "Nom", accessor: "nom_classe", className: "p-3" },
  { header: "Niveau", accessor: "niveau", className: "p-3" },
  { header: "Enseignant", accessor: "enseignant", className: "p-3" },
  { header: "Élèves", accessor: "nb_eleves", className: "p-3" },
  { header: "Salle", accessor: "salle", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

#### **3. Salles**
```jsx
// Colonnes spécifiques
const columns = [
  { header: "Nom", accessor: "nom_salle", className: "p-3" },
  { header: "Type", accessor: "type_salle", className: "p-3" },
  { header: "Capacité", accessor: "capacite", className: "p-3" },
  { header: "Équipements", accessor: "equipements", className: "p-3" },
  { header: "Statut", accessor: "statut", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

#### **4. Matières**
```jsx
// Colonnes spécifiques
const columns = [
  { header: "Matière (FR)", accessor: "nom_matiere_fr", className: "p-3" },
  { header: "Matière (AR)", accessor: "nom_matiere_ar", className: "p-3" },
  { header: "Description", accessor: "description", className: "p-3" },
  { header: "Enseignants", accessor: "enseignants", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

#### **5. Activités**
```jsx
// Colonnes spécifiques
const columns = [
  { header: "Titre", accessor: "titre", className: "p-3" },
  { header: "Type", accessor: "type", className: "p-3" },
  { header: "Date", accessor: "date", className: "p-3" },
  { header: "Lieu", accessor: "lieu", className: "p-3" },
  { header: "Participants", accessor: "participants", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

#### **6. Transport**
```jsx
// Colonnes spécifiques
const columns = [
  { header: "Véhicule", accessor: "vehicule", className: "p-3" },
  { header: "Chauffeur", accessor: "chauffeur", className: "p-3" },
  { header: "Itinéraire", accessor: "itineraire", className: "p-3" },
  { header: "Capacité", accessor: "capacite", className: "p-3" },
  { header: "Statut", accessor: "statut", className: "p-3" },
  ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
];
```

## 🎨 **Styles Standards**

### **Couleurs des Actions**
```jsx
// Bouton Voir
className="text-blue-600 hover:text-blue-900"

// Bouton Modifier  
className="text-indigo-600 hover:text-indigo-900"

// Bouton Supprimer
className="text-red-600 hover:text-red-900"
```

### **Badges de Statut**
```jsx
<span className={`px-2 py-1 rounded-full text-xs font-medium ${
  statut === 'actif' ? 'bg-green-100 text-green-800' :
  statut === 'terminé' ? 'bg-gray-100 text-gray-800' :
  statut === 'suspendu' ? 'bg-yellow-100 text-yellow-800' :
  'bg-red-100 text-red-800'
}`}>
  {statut}
</span>
```

### **Icônes par Page**
- **Élèves** : `<Users />`
- **Enseignants** : `<Users />`
- **Parents** : `<Users />`
- **Classes** : `<Users />`
- **Salles** : `<Building />`
- **Matières** : `<BookOpen />`
- **Activités** : `<Calendar />`
- **Transport** : `<Truck />`

## 🔄 **Template de Migration**

### **1. Imports**
```jsx
import Table from "../../components/Table";
import Pagination from "../../components/Pagination";
import { usePagination } from "../../hooks/usePagination";
import { Eye, Edit, Trash2, IconName } from "lucide-react";
```

### **2. Configuration**
```jsx
// Pagination
const {
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  paginatedData: paginatedItems,
  setCurrentPage,
  setItemsPerPage,
  resetPagination
} = usePagination({
  data: filteredItems,
  initialItemsPerPage: 15
});

// Colonnes
const columns = [
  // ... colonnes spécifiques
];

// Rendu des lignes
const renderRow = (item: ItemType) => (
  // ... rendu spécifique
);
```

### **3. Affichage**
```jsx
{loading ? (
  // Loading state
) : paginatedItems.length === 0 ? (
  // Empty state
) : (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <Table columns={columns} data={paginatedItems} renderRow={renderRow} />
    {filteredItems.length > 0 && (
      <div className="px-6 py-3 border-gray-200 text-sm">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          showItemsPerPage={true}
        />
      </div>
    )}
  </div>
)}
```

## 🎯 **Avantages du Design Standard**

### **Cohérence**
- ✅ **Interface uniforme** sur toute l'application
- ✅ **Navigation intuitive** avec les mêmes contrôles
- ✅ **Actions standardisées** avec couleurs cohérentes

### **Performance**
- ✅ **Pagination optimisée** avec le hook usePagination
- ✅ **Rendu efficace** des grandes listes
- ✅ **États gérés** (loading, vide, erreur)

### **Maintenance**
- ✅ **Code réutilisable** avec le même pattern
- ✅ **Modifications centralisées** dans les composants
- ✅ **Évolutivité** facilitée

## 🚀 **Prochaines Étapes**

1. **Migrer Parents** : Adapter les colonnes et renderRow
2. **Migrer Classes** : Adapter les données spécifiques
3. **Migrer Salles** : Adapter les équipements
4. **Migrer Matières** : Adapter les langues
5. **Migrer Activités** : Adapter les dates
6. **Migrer Transport** : Adapter les véhicules

**Toutes les pages auront le même design professionnel et cohérent !** 🎉
