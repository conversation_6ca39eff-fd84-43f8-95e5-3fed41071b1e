import React, { useState, useEffect } from 'react';
import { X, Save, Bus, DollarSign, Users, MapPin } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import { addTransport, updateTransport } from '../services/api';
import { Transport } from '../types';

interface TransportFormProps {
  transport?: Transport | null;
  onClose: () => void;
  onSuccess: () => void;
}

const TransportForm: React.FC<TransportFormProps> = ({ transport, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    trajet: '',
    matricule: '',
    prix: '',
    capacite: ''
  });

  useEffect(() => {
    if (transport) {
      setFormData({
        trajet: transport.trajet || '',
        matricule: transport.matricule || '',
        prix: transport.prix?.toString() || '',
        capacite: transport.capacite?.toString() || ''
      });
    }
  }, [transport]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.trajet.trim()) {
      setError('Le trajet est obligatoire');
      return false;
    }
    if (!formData.matricule.trim()) {
      setError('Le matricule est obligatoire');
      return false;
    }
    if (!formData.prix || isNaN(Number(formData.prix)) || Number(formData.prix) < 0) {
      setError('Le prix doit être un nombre positif');
      return false;
    }
    if (!formData.capacite || isNaN(Number(formData.capacite)) || Number(formData.capacite) < 1) {
      setError('La capacité doit être un nombre positif');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const dataToSubmit = {
        trajet: formData.trajet.trim(),
        matricule: formData.matricule.trim().toUpperCase(),
        prix: Number(formData.prix),
        capacite: Number(formData.capacite)
      };

      if (transport) {
        await updateTransport(transport.id_transport, dataToSubmit);
        setSuccessMessage('Transport modifié avec succès');
      } else {
        await addTransport(dataToSubmit);
        setSuccessMessage('Transport créé avec succès');
      }

      setTimeout(() => {
        onSuccess();
      }, 1500);

    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError(error.response?.data?.message || 'Erreur lors de la sauvegarde du transport');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[95vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {transport ? 'Modifier le transport' : 'Nouveau transport'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {successMessage}
            </div>
          )}

          {/* Informations du transport */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Informations du transport</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Trajet"
                name="trajet"
                value={formData.trajet}
                onChange={handleInputChange}
                required
                leftIcon={<MapPin size={16} />}
                placeholder="Ex: Casablanca - École"
              />

              <Input
                label="Matricule"
                name="matricule"
                value={formData.matricule}
                onChange={handleInputChange}
                required
                leftIcon={<Bus size={16} />}
                placeholder="Ex: 123456-A-12"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Prix mensuel (DH)"
                name="prix"
                type="number"
                value={formData.prix}
                onChange={handleInputChange}
                required
                leftIcon={<DollarSign size={16} />}
                min="0"
                step="0.01"
                placeholder="Ex: 150"
              />

              <Input
                label="Capacité (nombre de places)"
                name="capacite"
                type="number"
                value={formData.capacite}
                onChange={handleInputChange}
                required
                leftIcon={<Users size={16} />}
                min="1"
                placeholder="Ex: 25"
              />
            </div>
          </div>

          {/* Informations supplémentaires */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Informations importantes</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Le matricule doit être unique pour chaque transport</li>
              <li>• La capacité représente le nombre maximum d'élèves</li>
              <li>• Le prix est facturé mensuellement aux familles</li>
              <li>• Le trajet doit être descriptif (origine - destination)</li>
            </ul>
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
              icon={<Save size={16} />}
            >
              {loading ? 'Enregistrement...' : (transport ? 'Modifier' : 'Créer')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TransportForm;
