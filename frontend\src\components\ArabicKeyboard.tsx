import React from 'react';

type ArabicKeyboardProps = {
  onKeyPress: (char: string) => void;
  onClose?: () => void;
};

const keys = [
  'ض','ص','ث','ق','ف','غ','ع','ه','خ','ح','ج','د',
  'ش','س','ي','ب','ل','ا','ت','ن','م','ك','ط','ذ',
  'ء','ؤ','ر','ى','ة','و','ز','ظ','إ','أ','ئ','لا','←'
];

export const ArabicKeyboard: React.FC<ArabicKeyboardProps> = ({ onKeyPress, onClose }) => {
  return (
    <div className="flex flex-wrap bg-white border border-gray-300 shadow-xl p-3 w-[380px] mt-2 rounded-lg z-[9999] relative">
      <div className="w-full mb-2 flex items-center justify-between">
        <span className="text-sm text-gray-600 font-medium">لوحة المفاتيح العربية</span>
        {onClose && (
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            className="text-gray-400 hover:text-gray-600 text-lg font-bold"
          >
            ×
          </button>
        )}
      </div>
      {keys.map((key, index) => (
        <button
          key={index}
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onKeyPress(key);
          }}
          className={`w-8 h-8 m-0.5 border border-gray-200 rounded text-base font-medium transition-colors
            ${key === '←'
              ? 'bg-red-50 hover:bg-red-100 text-red-600 border-red-200'
              : 'bg-gray-50 hover:bg-blue-50 text-gray-800 hover:border-blue-300'
            }
            active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500`}
        >
          {key}
        </button>
      ))}
      <div className="w-full mt-2 flex justify-center">
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onKeyPress(' ');
          }}
          className="px-8 py-1 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded text-sm font-medium transition-colors"
        >
          مسافة
        </button>
      </div>
    </div>
  );
};
export default ArabicKeyboard;