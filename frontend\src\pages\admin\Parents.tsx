import React, { useEffect, useState } from "react";
import { getParents, deleteParent, deleteUser } from "../../services/api";
import { useAuth } from "../../context/AuthContext";
import Table from "../../components/Table";
import Modal from "../../components/Modal";
import Button from "../../components/Button";
import { Search, Filter, Plus, Eye, Edit, Trash2, Users } from "lucide-react";
import ParentForm from "../../components/ParentForm";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";
import { Parent } from "../../types";
import Pagination from "../../components/Pagination";
import { usePagination } from "../../hooks/usePagination";

export default function Parents() {
  const { user } = useAuth();
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();
  const [parents, setParents] = useState<Parent[]>([]);
  const [parentToEdit, setParentToEdit] = useState<Parent | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    fetchParents();
  }, []);

  const fetchParents = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await getParents();
      setParents(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error("Erreur lors de la récupération des parents :", error);
      setError("Impossible de charger les parents.");
    } finally {
      setLoading(false);
    }
  };

  
  const handleDelete = async (id_user: number | undefined, id_parent: number) => {
    try {
      await deleteParent(id_parent);
      await deleteUser(id_user);
      fetchParents();
    } catch (error) {
      console.error("Erreur lors de la suppression :", error);
      setError("Impossible de supprimer le parent.");
    }
  };

  const handleDeleteClick = (parent: Parent) => {
    const parentName = `${parent.user?.prenom || ''} ${parent.user?.nom || ''}`.trim();
    confirmDelete(
      () => handleDelete(parent.user?.id_utilisateur, parent.id_parent),
      parentName,
      "Cette action supprimera définitivement ce parent et toutes ses données associées."
    );
  };

  const handleViewDetails = (parent: Parent) => {
    // Fonction pour voir les détails du parent
    console.log("Voir détails parent:", parent);
    // TODO: Implémenter la modal de détails
  };

  const columns = [
    { header: "Nom", accessor: "nom", className: "p-3" },
    { header: "Prénom", accessor: "prenom", className: "p-3" },
    { header: "النسب", accessor: "nom_ar", className: "p-3 hidden md:table-cell" },
    { header: "الإسم", accessor: "prenom_ar", className: "p-3 hidden md:table-cell" },
    { header: "CIN", accessor: "cin", className: "p-3" },
    { header: "Téléphone", accessor: "telephone", className: "p-3" },
    { header: "Adresse", accessor: "adresse", className: "p-3 hidden md:table-cell" },
    { header: "Sexe", accessor: "sexe", className: "p-3" },
    ...(user?.role === "admin" ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
  ];

 
  const renderRow = (parent: Parent) => (
    <tr key={parent.id_parent} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3 font-semibold">{parent.user?.nom || "-"}</td>
      <td className="p-3">{parent.user?.prenom || "-"}</td>
      <td className="p-3 hidden md:table-cell">{parent.nom_ar || "-"}</td>
      <td className="p-3 hidden md:table-cell">{parent.prenom_ar || "-"}</td>
      <td className="p-3">{parent.num_CIN || "-"}</td>
      <td className="p-3">{parent.user?.telephone || "-"}</td>
      <td className="p-3 hidden md:table-cell">{parent.user?.adresse || "-"}</td>
      <td className="p-3">
        {parent.user?.sexe === "homme"? "H" : parent.user?.sexe === "femme"? "F": "-"}
      </td>
      {user?.role === "admin" && (
        <td className="p-3">
          <div className="flex space-x-2">
            <button
              className="text-blue-600 hover:text-blue-900"
              onClick={() => handleViewDetails(parent)}
              title="Voir les détails"
            >
              <Eye size={16} />
            </button>
            <button
              className="text-indigo-600 hover:text-indigo-900"
              onClick={() => {
                setParentToEdit(parent);
                setShowModal(true);
              }}
              title="Modifier"
            >
              <Edit size={16} />
            </button>
            <button
              className="text-red-600 hover:text-red-900"
              onClick={() => handleDeleteClick(parent)}
              title="Supprimer le parent"
            >
              <Trash2 size={16} />
            </button>
          </div>
        </td>
      )}
    </tr>
  );


  const filteredParents = parents.filter(
    (p) =>
      p.user?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.user?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.num_CIN?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.user?.adresse?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedParents,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredParents,
    initialItemsPerPage: 15
  });

  return (
    <div className="space-y-6">

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher un parent..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        <div className="flex space-x-2">
          <Button icon={<Filter size={16} />} variant="outline">Filtres</Button>
          {/* <Button icon={<Plus size={16} />} variant="primary" onClick={() => {
            setParentToEdit(null);
            setShowModal(true);}}>
            Ajouter un parent
          </Button> */}
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-md">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : paginatedParents.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Users className="mx-auto mb-2 w-10 h-10" />
            <p>Aucun parent trouvé</p>
            {searchTerm && (
              <p className="text-sm">
                Essayez de modifier votre recherche ou{" "}
                <button onClick={() => setSearchTerm('')} className="text-blue-600 underline">
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <Table columns={columns} data={paginatedParents} renderRow={renderRow} />
          {/* Pagination */}
          {filteredParents.length > 0 && (
            <div className="px-6 py-3 border-gray-200 text-sm">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={setCurrentPage}
                onItemsPerPageChange={setItemsPerPage}
                showItemsPerPage={true}
              />
            </div>
          )}
        </div>
        )}
      </div>

      {/* MODAL */}
      <Modal 
        isOpen={showModal} 
        onClose={() => setShowModal(false)}
        title={parentToEdit ? "Modification d'un parent" : "Ajout d'un parent"}
      >
        
        <ParentForm
          initialParent={parentToEdit}
          onSuccess={() => {
            setShowModal(false);
            fetchParents();
          }}
        />
      </Modal>

      {/* Modal de confirmation de suppression */}
      <ConfirmationComponent />
    </div>
  );
}
