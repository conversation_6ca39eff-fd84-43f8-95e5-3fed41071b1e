-- Script de test pour valider la restriction des paiements de transport
-- Ce script permet de tester que seuls les élèves avec transport peuvent avoir des paiements de transport

-- 1. Vérifier les élèves existants
SELECT 
    e.id_eleve,
    u.nom,
    u.prenom,
    e.code_massar,
    'Sans transport' as statut_transport
FROM eleve e
JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
WHERE e.id_eleve NOT IN (
    SELECT DISTINCT bt.id_eleve 
    FROM beneficier_transport bt
    JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
    WHERE ans.est_active = true
)
LIMIT 5;

-- 2. Vérifier les élèves avec transport
SELECT 
    e.id_eleve,
    u.nom,
    u.prenom,
    e.code_massar,
    t.trajet,
    t.prix,
    'Avec transport' as statut_transport
FROM eleve e
JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
JOIN beneficier_transport bt ON e.id_eleve = bt.id_eleve
JOIN transport t ON bt.id_transport = t.id_transport
JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
WHERE ans.est_active = true
LIMIT 5;

-- 3. Test de la requête utilisée dans le backend pour valider le transport
-- Remplacez :id_eleve et :id_annee_scolaire par des valeurs réelles pour tester

-- Exemple pour un élève SANS transport (devrait retourner 0)
-- SELECT COUNT(*) as count 
-- FROM beneficier_transport bt
-- WHERE bt.id_eleve = 1  -- Remplacer par un ID d'élève sans transport
-- AND bt.id_annee_scolaire = 1;  -- Remplacer par l'ID de l'année active

-- Exemple pour un élève AVEC transport (devrait retourner 1 ou plus)
-- SELECT COUNT(*) as count 
-- FROM beneficier_transport bt
-- WHERE bt.id_eleve = 2  -- Remplacer par un ID d'élève avec transport
-- AND bt.id_annee_scolaire = 1;  -- Remplacer par l'ID de l'année active

-- 4. Vérifier l'année scolaire active
SELECT id_annee_scolaire, libelle, est_active 
FROM annee_scolaire 
WHERE est_active = true;

-- 5. Statistiques des transports
SELECT 
    'Total élèves' as type,
    COUNT(*) as nombre
FROM eleve
UNION ALL
SELECT 
    'Élèves avec transport' as type,
    COUNT(DISTINCT bt.id_eleve) as nombre
FROM beneficier_transport bt
JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
WHERE ans.est_active = true
UNION ALL
SELECT 
    'Élèves sans transport' as type,
    (SELECT COUNT(*) FROM eleve) - COUNT(DISTINCT bt.id_eleve) as nombre
FROM beneficier_transport bt
JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
WHERE ans.est_active = true;
