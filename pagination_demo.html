<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration Pagination</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Démonstration de la Pagination</h1>
        
        <!-- Exemple de tableau avec pagination -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    Liste des absences (156)
                </h3>
            </div>

            <!-- Tableau simulé -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Élève</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Motif</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Lignes simulées -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i data-lucide="users" class="w-8 h-8 text-gray-400 mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Ahmed Ben Ali</div>
                                        <div class="text-sm text-gray-500">12345 • 6ème A</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <div>Du 15/01/2024 08:00</div>
                                    <div>Au 15/01/2024 12:00</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i data-lucide="clock" class="w-4 h-4 text-gray-400 mr-1"></i>
                                    <span class="text-sm text-gray-900">4h</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-red-600 bg-red-100">
                                    maladie
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-green-600 bg-green-100">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                    Justifiée
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="text-indigo-600 hover:text-indigo-900">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- Répéter pour simuler plus de lignes -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i data-lucide="users" class="w-8 h-8 text-gray-400 mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Fatima El Mansouri</div>
                                        <div class="text-sm text-gray-500">67890 • 5ème B</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    <div>Du 16/01/2024 09:00</div>
                                    <div>Au 16/01/2024 10:30</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i data-lucide="clock" class="w-4 h-4 text-gray-400 mr-1"></i>
                                    <span class="text-sm text-gray-900">1h 30min</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                    familial
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-red-600 bg-red-100">
                                    <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>
                                    Non justifiée
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <button class="text-indigo-600 hover:text-indigo-900">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        
                        <!-- Plus de lignes... -->
                        <tr><td colspan="6" class="px-6 py-4 text-center text-gray-500 text-sm">... 8 autres lignes ...</td></tr>
                    </tbody>
                </table>
            </div>

            <!-- Composant de Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                    <!-- Informations sur les éléments -->
                    <div class="text-sm text-gray-700">
                        Affichage de <span class="font-medium">1</span> à 
                        <span class="font-medium">10</span> sur 
                        <span class="font-medium">156</span> résultats
                    </div>

                    <div class="flex flex-col sm:flex-row items-center gap-4">
                        <!-- Sélecteur d'éléments par page -->
                        <div class="flex items-center gap-2">
                            <label class="text-sm text-gray-700">Éléments par page:</label>
                            <select class="px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-600 focus:border-blue-600">
                                <option selected>10</option>
                                <option>25</option>
                                <option>50</option>
                                <option>100</option>
                            </select>
                        </div>

                        <!-- Navigation des pages -->
                        <nav class="flex items-center gap-1">
                            <!-- Première page -->
                            <button disabled class="p-2 rounded-md border border-gray-300 text-gray-300 cursor-not-allowed" title="Première page">
                                <i data-lucide="chevrons-left" class="w-4 h-4"></i>
                            </button>

                            <!-- Page précédente -->
                            <button disabled class="p-2 rounded-md border border-gray-300 text-gray-300 cursor-not-allowed" title="Page précédente">
                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                            </button>

                            <!-- Numéros de pages -->
                            <div class="flex items-center gap-1">
                                <button class="px-3 py-2 rounded-md text-sm font-medium bg-blue-600 text-white">1</button>
                                <button class="px-3 py-2 rounded-md text-sm font-medium border border-gray-300 text-gray-700 hover:bg-gray-50">2</button>
                                <button class="px-3 py-2 rounded-md text-sm font-medium border border-gray-300 text-gray-700 hover:bg-gray-50">3</button>
                                <span class="px-3 py-2 text-gray-500">...</span>
                                <button class="px-3 py-2 rounded-md text-sm font-medium border border-gray-300 text-gray-700 hover:bg-gray-50">16</button>
                            </div>

                            <!-- Page suivante -->
                            <button class="p-2 rounded-md border border-gray-300 text-gray-500 hover:bg-gray-50" title="Page suivante">
                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                            </button>

                            <!-- Dernière page -->
                            <button class="p-2 rounded-md border border-gray-300 text-gray-500 hover:bg-gray-50" title="Dernière page">
                                <i data-lucide="chevrons-right" class="w-4 h-4"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informations sur l'implémentation -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">✅ Fonctionnalités Implémentées</h3>
                <ul class="text-sm text-blue-800 space-y-2">
                    <li>• Navigation complète (première, précédente, suivante, dernière)</li>
                    <li>• Numéros de pages avec points de suspension intelligents</li>
                    <li>• Sélecteur d'éléments par page (10, 25, 50, 100)</li>
                    <li>• Informations contextuelles (X à Y sur Z résultats)</li>
                    <li>• Design responsive et accessible</li>
                    <li>• Réinitialisation automatique lors du filtrage</li>
                </ul>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-green-900 mb-4">🚀 Pages Avec Pagination</h3>
                <ul class="text-sm text-green-800 space-y-2">
                    <li>• <strong>Présences</strong> : 10 éléments par défaut</li>
                    <li>• <strong>Paiements</strong> : 15 éléments par défaut</li>
                    <li>• <strong>Élèves</strong> : 20 éléments par défaut</li>
                    <li>• <strong>Composant réutilisable</strong> pour futures pages</li>
                    <li>• <strong>Hook personnalisé</strong> pour la logique</li>
                    <li>• <strong>Performance optimisée</strong> pour grandes listes</li>
                </ul>
            </div>
        </div>

        <!-- Instructions de test -->
        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-900 mb-4">🧪 Comment Tester</h3>
            <div class="text-sm text-yellow-800">
                <ol class="list-decimal pl-5 space-y-1">
                    <li>Aller sur <code>/admin/presences</code>, <code>/admin/paiements</code>, ou <code>/admin/eleves</code></li>
                    <li>Vérifier que la pagination s'affiche en bas du tableau</li>
                    <li>Tester la navigation entre les pages</li>
                    <li>Changer le nombre d'éléments par page</li>
                    <li>Appliquer des filtres et vérifier la réinitialisation</li>
                    <li>Vérifier les informations de contexte</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Initialiser les icônes Lucide
        lucide.createIcons();
    </script>
</body>
</html>
