<?php

class Bulletin {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Récupérer toutes les données nécessaires pour un bulletin
    public function getBulletinData($id_eleve, $semestre) {
        try {
            // Informations de l'élève
            $eleve = $this->getEleveInfo($id_eleve);
            if (!$eleve) {
                return false;
            }

            // Notes par matière
            $notes = $this->getNotesParMatiere($id_eleve, $semestre);
            
            // Moyennes
            $moyennes = $this->calculerMoyennes($notes);

            // Année scolaire active
            $anneeScolaire = $this->getAnneeScolaireActive();

            return [
                'eleve' => $eleve,
                'notes' => $notes,
                'moyennes' => $moyennes,
                'semestre' => $semestre,
                'annee_scolaire' => $anneeScolaire
            ];

        } catch (Exception $e) {
            error_log("Erreur dans getBulletinData: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les informations de l'élève
    private function getEleveInfo($id_eleve) {
        $query = "
            SELECT 
                e.id_eleve,
                e.code_massar,
                u.nom,
                u.prenom,
                u.date_naissance,
                u.lieu_naissance,
                u.sexe,
                c.nom_classe as classe
            FROM eleve e
            JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
            JOIN inscription i ON e.id_eleve = i.id_eleve
            JOIN classe c ON i.id_classe = c.id_classe
            JOIN annee_scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
            WHERE e.id_eleve = ? AND a.est_active = true
        ";

        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$id_eleve]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Récupérer les notes par matière pour un semestre
    private function getNotesParMatiere($id_eleve, $semestre) {
        $query = "
            SELECT 
                m.nom_matiere_fr as matiere,
                e.type_examen,
                n.note
            FROM note n
            JOIN examen e ON n.id_examen = e.id_examen
            JOIN matiere m ON e.id_matiere = m.id_matiere
            WHERE n.id_eleve = ? AND e.semestre = ?
            ORDER BY m.nom_matiere_fr, e.type_examen
        ";

        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$id_eleve, $semestre]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organiser les notes par matière
        $notesByMatiere = [];
        foreach ($results as $row) {
            $matiere = $row['matiere'];
            $type = $row['type_examen'];
            $note = $row['note'];

            if (!isset($notesByMatiere[$matiere])) {
                $notesByMatiere[$matiere] = [
                    'contrôle' => null,
                    'devoir' => null,
                    'examen' => null,
                    'participation' => null
                ];
            }

            $notesByMatiere[$matiere][$type] = $note;
        }

        // Calculer la moyenne pour chaque matière
        foreach ($notesByMatiere as $matiere => &$notes) {
            $notes['moyenne'] = $this->calculerMoyenneMatiere($notes);
        }

        return $notesByMatiere;
    }

    // Calculer la moyenne d'une matière
    private function calculerMoyenneMatiere($notes) {
        $totalNotes = 0;
        $nombreNotes = 0;
        $coefficients = [
            'contrôle' => 1,
            'devoir' => 2,
            'examen' => 3,
            'participation' => 1
        ];

        $totalCoefficients = 0;

        foreach ($notes as $type => $note) {
            if ($type !== 'moyenne' && $note !== null) {
                $coeff = $coefficients[$type] ?? 1;
                $totalNotes += $note * $coeff;
                $totalCoefficients += $coeff;
                $nombreNotes++;
            }
        }

        return $totalCoefficients > 0 ? $totalNotes / $totalCoefficients : 0;
    }

    // Calculer les moyennes générales
    private function calculerMoyennes($notesByMatiere) {
        $totalMoyennes = 0;
        $nombreMatieres = 0;

        foreach ($notesByMatiere as $matiere => $notes) {
            if ($notes['moyenne'] > 0) {
                $totalMoyennes += $notes['moyenne'];
                $nombreMatieres++;
            }
        }

        $moyenneGenerale = $nombreMatieres > 0 ? $totalMoyennes / $nombreMatieres : 0;

        return [
            'moyenne_generale' => $moyenneGenerale,
            'nombre_matieres' => $nombreMatieres
        ];
    }

    // Récupérer l'année scolaire active
    private function getAnneeScolaireActive() {
        $query = "SELECT * FROM annee_scolaire WHERE est_active = true LIMIT 1";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Récupérer les bulletins de tous les élèves d'une classe
    public function getBulletinsClasse($id_classe, $semestre) {
        try {
            // Récupérer tous les élèves de la classe
            $query = "
                SELECT 
                    e.id_eleve,
                    u.nom,
                    u.prenom,
                    e.code_massar
                FROM Eleve e
                JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN Inscription i ON e.id_eleve = i.id_eleve
                JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                WHERE i.id_classe = ? AND a.est_active = true
                ORDER BY u.nom, u.prenom
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_classe]);
            $eleves = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $bulletins = [];
            foreach ($eleves as $eleve) {
                $bulletinData = $this->getBulletinData($eleve['id_eleve'], $semestre);
                if ($bulletinData) {
                    $bulletins[] = [
                        'id_eleve' => $eleve['id_eleve'],
                        'nom' => $eleve['nom'],
                        'prenom' => $eleve['prenom'],
                        'code_massar' => $eleve['code_massar'],
                        'moyenne_generale' => $bulletinData['moyennes']['moyenne_generale'],
                        'bulletin_data' => $bulletinData
                    ];
                }
            }

            // Trier par moyenne décroissante
            usort($bulletins, function($a, $b) {
                return $b['moyenne_generale'] <=> $a['moyenne_generale'];
            });

            return $bulletins;

        } catch (Exception $e) {
            error_log("Erreur dans getBulletinsClasse: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les statistiques d'une classe pour un semestre
    public function getStatistiquesClasse($id_classe, $semestre) {
        try {
            $bulletins = $this->getBulletinsClasse($id_classe, $semestre);
            
            if (empty($bulletins)) {
                return false;
            }

            $moyennes = array_column($bulletins, 'moyenne_generale');
            $moyennes = array_filter($moyennes, function($m) { return $m > 0; });

            if (empty($moyennes)) {
                return false;
            }

            $stats = [
                'nombre_eleves' => count($bulletins),
                'moyenne_classe' => array_sum($moyennes) / count($moyennes),
                'moyenne_max' => max($moyennes),
                'moyenne_min' => min($moyennes),
                'nombre_admis' => count(array_filter($moyennes, function($m) { return $m >= 10; })),
                'taux_reussite' => (count(array_filter($moyennes, function($m) { return $m >= 10; })) / count($moyennes)) * 100
            ];

            return $stats;

        } catch (Exception $e) {
            error_log("Erreur dans getStatistiquesClasse: " . $e->getMessage());
            return false;
        }
    }
}
?>
