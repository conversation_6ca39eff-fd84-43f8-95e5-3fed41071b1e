import React from 'react';
import { Edit, Trash2, Eye, MoreHorizontal } from 'lucide-react';
import Pagination from './Pagination';

interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: any) => React.ReactNode;
}

interface Action {
  label: string;
  icon: React.ReactNode;
  onClick: (row: any) => void;
  color?: 'blue' | 'indigo' | 'red' | 'green' | 'yellow';
  show?: (row: any) => boolean;
}

interface StandardTableProps {
  title: string;
  columns: Column[];
  data: any[];
  actions?: Action[];
  loading?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  // Pagination
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  itemsPerPage?: number;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showPagination?: boolean;
}

const StandardTable: React.FC<StandardTableProps> = ({
  title,
  columns,
  data,
  actions = [],
  loading = false,
  emptyMessage = "Aucune donnée trouvée",
  emptyIcon,
  currentPage = 1,
  totalPages = 1,
  totalItems = 0,
  itemsPerPage = 10,
  onPageChange,
  onItemsPerPageChange,
  showPagination = true
}) => {
  const getActionColor = (color: string = 'blue') => {
    const colors = {
      blue: 'text-blue-600 hover:text-blue-900',
      indigo: 'text-indigo-600 hover:text-indigo-900',
      red: 'text-red-600 hover:text-red-900',
      green: 'text-green-600 hover:text-green-900',
      yellow: 'text-yellow-600 hover:text-yellow-900'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const renderCellValue = (column: Column, row: any) => {
    const value = row[column.key];
    
    if (column.render) {
      return column.render(value, row);
    }
    
    return value;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-3 text-gray-600">Chargement...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* En-tête */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          {title} ({totalItems})
        </h3>
      </div>

      {/* Tableau */}
      {data.length === 0 ? (
        <div className="text-center py-12">
          {emptyIcon && <div className="mx-auto mb-4 w-12 h-12 text-gray-400">{emptyIcon}</div>}
          <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune donnée</h3>
          <p className="mt-1 text-sm text-gray-500">{emptyMessage}</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column) => (
                    <th
                      key={column.key}
                      className={`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${
                        column.align === 'center' ? 'text-center' :
                        column.align === 'right' ? 'text-right' : 'text-left'
                      }`}
                      style={column.width ? { width: column.width } : undefined}
                    >
                      {column.label}
                    </th>
                  ))}
                  {actions.length > 0 && (
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.map((row, index) => (
                  <tr key={row.id || index} className="hover:bg-gray-50">
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${
                          column.align === 'center' ? 'text-center' :
                          column.align === 'right' ? 'text-right' : 'text-left'
                        }`}
                      >
                        {renderCellValue(column, row)}
                      </td>
                    ))}
                    {actions.length > 0 && (
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {actions.map((action, actionIndex) => {
                            if (action.show && !action.show(row)) {
                              return null;
                            }
                            return (
                              <button
                                key={actionIndex}
                                onClick={() => action.onClick(row)}
                                className={`${getActionColor(action.color)} transition-colors`}
                                title={action.label}
                              >
                                {action.icon}
                              </button>
                            );
                          })}
                        </div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {showPagination && totalItems > 0 && onPageChange && onItemsPerPageChange && (
            <div className="px-6 py-4 border-t border-gray-200">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={onPageChange}
                onItemsPerPageChange={onItemsPerPageChange}
                showItemsPerPage={true}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default StandardTable;
