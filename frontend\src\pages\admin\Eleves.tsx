import React, { useEffect, useState } from "react";
import { getEleves, deleteEleve, getAnneeScolaireActive, getAnneesScolaires, getClasses, getNiveaux, getElevesEnseignant, getClassesEnseignant, getEnseignants } from "../../services/api";
import { useAuth } from "../../context/AuthContext";
import Table from "../../components/Table";
import Modal from "../../components/Modal";
import Button from "../../components/Button";
import { Search, Filter, Plus, Eye, Edit, Trash2, Users } from "lucide-react";
import EleveFormCorrige from "../../components/EleveForm";
import EleveDetailModal from "../../components/EleveDetailModal";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";
import { Classe, Eleve, Niveau } from "../../types";
import Select from "../../components/Select";
import Pagination from "../../components/Pagination";
import { usePagination } from "../../hooks/usePagination";

export default function Eleves() {
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();
  const [eleves, setEleves] = useState<Eleve[]>([]);
  const [eleveToEdit, setEleveToEdit] = useState<Eleve | null>(null);
  const [eleveToView, setEleveToView] = useState<Eleve | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  // Debug: Afficher les informations de l'utilisateur
  console.log("🔍 DEBUG - Utilisateur connecté:", user);
  console.log("🔍 DEBUG - Rôle utilisateur:", user?.role);
  console.log("🔍 DEBUG - ID utilisateur:", user?.id_utilisateur);

  const [annees, setAnnees] = useState<any[]>([]);
  const [anneeSelected, setAnneeSelected] = useState<number | undefined>();
  const [anneeActive, setAnneeActive] = useState<number | undefined>();
  const [showFilters, setShowFilters] = useState(false);
  const [niveaux, setNiveaux] = useState<Niveau[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [niveauSelected, setNiveauSelected] = useState<number | undefined>();
  const [classeSelected, setClasseSelected] = useState<number | undefined>();

  useEffect(() => {
    const fetchAnneesScolaires = async () => {
      const res = await getAnneesScolaires();
      setAnnees(res.data.data || []);
      const activeRes = await getAnneeScolaireActive();
      if (activeRes.data?.data?.id_annee_scolaire) {
        const idAnneeActive = activeRes.data.data.id_annee_scolaire;
        setAnneeActive(idAnneeActive);
        setAnneeSelected(idAnneeActive);
      } else {
        // Si pas d'année active, charger tous les élèves
        fetchElevesByAnnee();
      }
    };
    fetchAnneesScolaires();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const niveauxRes = await getNiveaux();
      console.log("Niveaux reçus :", niveauxRes.data.data);
      setNiveaux(niveauxRes.data.data || []);

      if (user?.role === 'enseignant') {
        // Pour les enseignants, récupérer seulement leurs classes
        try {
          // D'abord récupérer l'enseignant connecté
          const enseignantsRes = await getEnseignants();
          const enseignantsData = enseignantsRes.data?.data || [];
          const enseignantConnecte = enseignantsData.find((ens: any) => ens.id_utilisateur === user.id_utilisateur);

          if (enseignantConnecte) {
            const classesRes = await getClassesEnseignant(enseignantConnecte.id_enseignant);
            setClasses(classesRes.data.data || []);
            console.log("Classes de l'enseignant:", classesRes.data.data);
          } else {
            setClasses([]);
          }
        } catch (error) {
          console.error("Erreur lors du chargement des classes de l'enseignant:", error);
          setClasses([]);
        }
      } else {
        // Pour les admins, récupérer toutes les classes
        const classesRes = await getClasses();
        setClasses(classesRes.data.data || []);
        console.log("Toutes les classes:", classesRes.data.data);
      }
    };
    fetchData();
  }, [user]);

  const fetchElevesByAnnee = async () => {
    setLoading(true);
    try {
      console.log("🔍 Chargement des élèves - Année:", anneeSelected);
      console.log("👤 User role:", user?.role);

      if (user?.role === 'enseignant') {
        // Pour les enseignants, récupérer seulement leurs élèves
        console.log("🧑‍🏫 Enseignant - Récupération de ses élèves");

        // D'abord récupérer l'enseignant connecté
        const enseignantsRes = await getEnseignants();
        const enseignantsData = enseignantsRes.data?.data || [];
        const enseignantConnecte = enseignantsData.find((ens: any) => ens.id_utilisateur === user.id_utilisateur);

        if (enseignantConnecte) {
          console.log("📚 Récupération des élèves de l'enseignant ID:", enseignantConnecte.id_enseignant);
          const res = await getElevesEnseignant(enseignantConnecte.id_enseignant);
          console.log("✅ Réponse API élèves enseignant:", res.data);
          setEleves(res.data.data || []);
        } else {
          console.warn("⚠️ Enseignant connecté non trouvé");
          setEleves([]);
        }
      } else {
        // Pour les admins, récupérer tous les élèves
        console.log("👑 Admin - Récupération de tous les élèves");
        const res = await getEleves(anneeSelected || null);
        console.log("✅ Réponse API élèves admin:", res.data);
        setEleves(res.data.data || []);
      }
    } catch (err) {
      console.error("❌ Erreur lors du chargement des élèves:", err);
      setErrorMessage("Erreur lors du chargement des élèves.");
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchElevesByAnnee();
  }, [anneeSelected]);

  const handleDelete = async (id: number) => {
    try {
      await deleteEleve(id);
      fetchElevesByAnnee();
    } catch (error) {
      console.error("Erreur suppression :", error);
      setErrorMessage("Impossible de supprimer l'élève.");
    }
  };

  const handleDeleteClick = (eleve: Eleve) => {
    const name = `${eleve.user?.prenom || ''} ${eleve.user?.nom || ''}`.trim();
    confirmDelete(
      () => handleDelete(eleve.id_eleve),
      name,
      "Cette action supprimera définitivement cet élève et toutes ses données associées."
    );
  };

  const handleViewClick = (eleve: Eleve) => {
    setEleveToView(eleve);
    setShowDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setEleveToView(null);
  };

  const columns = [
    { header: "Nom", accessor: "nom", className: "p-3" },
    { header: "Prénom", accessor: "prenom", className: "p-3" },
    { header: "النسب", accessor: "nom_ar", className: "p-3 hidden md:table-cell" },
    { header: "الإسم", accessor: "prenom_ar", className: "p-3 hidden md:table-cell" },
    { header: "Code Massar", accessor: "code_massar", className: "p-3 hidden md:table-cell" },
    { header: "Classe", accessor: "classe", className: "p-3" },
    { header: "Sexe", accessor: "sexe", className: "p-3" },
    { header: "Adresse", accessor: "adresse", className: "p-3 hidden lg:table-cell" },
    ...(user?.role === 'admin' ? [{ header: "Actions", accessor: "actions", className: "p-3" }] : []),
  ];

  const renderRow = (eleve: Eleve) => (
    <tr key={eleve.id_eleve} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3 font-semibold">{eleve.user?.nom || "-"}</td>
      <td className="p-3">{eleve.user?.prenom || "-"}</td>
      <td className="p-3 hidden md:table-cell">{eleve.nom_ar || "-"}</td>
      <td className="p-3 hidden md:table-cell">{eleve.prenom_ar || "-"}</td>
      <td className="p-3 hidden md:table-cell">{eleve.code_massar || "-"}</td>
      <td className="p-3">{eleve.nom_classe || "-"}</td>
      <td className="p-3">{eleve.user?.sexe || "-"}</td>
      <td className="p-3 hidden lg:table-cell">{eleve.user?.adresse || "-"}</td>
      {user?.role !== 'enseignant' && (
        <td className="p-3">
          <div className="flex space-x-2">
            <button
              className="text-blue-600 hover:text-blue-900"
              onClick={() => handleViewClick(eleve)}
              title="Voir les détails"
            >
              <Eye size={16} />
            </button>
            {user?.role === 'admin' && (
              <>
                <button
                  className="text-indigo-600 hover:text-indigo-900"
                  onClick={() => {
                    setEleveToEdit(eleve);
                    setShowModal(true);
                  }}
                  title="Modifier"
                >
                  <Edit size={16} />
                </button>
                <button
                  className="text-red-600 hover:text-red-900"
                  onClick={() => handleDeleteClick(eleve)}
                  title="Supprimer"
                >
                  <Trash2 size={16} />
                </button>
              </>
          
            )}
          </div>
        </td>
      )}
    </tr>
  );

  let filteredEleves = eleves.filter(
    (el) =>
      el.user?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      el.user?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      el.code_massar?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      el.nom_classe?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (classeSelected) {
    filteredEleves = filteredEleves.filter((e) => e.id_classe === classeSelected);
  } else if (niveauSelected) {
    filteredEleves = filteredEleves.filter((e) => e.id_niveau === niveauSelected);
  }

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedEleves,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredEleves,
    initialItemsPerPage: 20
  });

  const filteredClasses = niveauSelected
    ? classes.filter((c) => c.id_niveau === niveauSelected)
    : classes;

  return (
    <div className="space-y-6">
      {errorMessage && (
        <div className="bg-red-100 border border-red-300 p-4 rounded text-red-800">
          {errorMessage}
        </div>
      )}

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher un élève..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        <div className="flex items-center gap-2">
          
          <Button icon={<Filter size={16} />} variant="outline" onClick={() => setShowFilters(!showFilters)}>
            Filtres
          </Button>
          {user?.role === 'admin' && (
            <Button icon={<Plus size={16} />} variant="primary" onClick={() => {
              setEleveToEdit(null);
              setShowModal(true);
            }}>
              Ajouter un élève
            </Button>
          )}
        </div>
      </div>

      {showFilters && (
        <div className="bg-white border rounded-lg p-4 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Année Scolaire"
              placeholder="Toutes les années"
              value={anneeSelected ? anneeSelected.toString() : ""}
              onChange={(e) => setAnneeSelected(e.target.value ? Number(e.target.value) : undefined)}
            >
              {annees.map((annee) => (
                <option key={annee.id_annee_scolaire} value={annee.id_annee_scolaire}>
                  {annee.libelle}
                </option>
              ))}
            </Select>
            <Select
              label="Niveau"
              placeholder="Tous les niveaux"
              value={niveauSelected ? niveauSelected.toString() : ""}
              onChange={e => setNiveauSelected(e.target.value ? Number(e.target.value) : undefined)}
            >
             {niveaux.map((niveau) => (
                <option key={niveau.id_niveau} value={niveau.id_niveau}>
                  {niveau.libelle}
                </option>
              ))}
            </Select>
            <Select
              label="Classe"
              placeholder="Choisir une classe"
              value={classeSelected ? classeSelected.toString() : ""}
              onChange={(e) => setClasseSelected(e.target.value ? Number(e.target.value) : undefined)}
            >
              {filteredClasses.map((classe) => (
                <option key={classe.id_classe} value={classe.id_classe}>
                  {classe.nom_classe}
                </option>
              ))}
            </Select>
            <div className="flex items-end">
              <Button variant="outline" onClick={() => {
                setAnneeSelected(anneeActive);
                setClasseSelected(undefined);
                setNiveauSelected(undefined);
                setSearchTerm('');
              }}>
                Réinitialiser
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : paginatedEleves.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Users className="mx-auto mb-2 w-10 h-10" />
            <p>Aucun élève trouvé</p>
            {searchTerm && (
              <p className="text-sm">
                Essayez de modifier votre recherche ou{" "}
                <button onClick={() => setSearchTerm('')} className="text-blue-600 underline">
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <Table columns={columns} data={paginatedEleves} renderRow={renderRow} />
          {/* Pagination */}
          {filteredEleves.length > 0 && (
            <div className="px-6 py-3 border-gray-200 text-sm">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={setCurrentPage}
                onItemsPerPageChange={setItemsPerPage}
                showItemsPerPage={true}
              />
            </div>
          )}
        </div>
        )}

        
      </div>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={eleveToEdit ? "Modification d'élève" : "Inscription d'élève"}
      >
        <EleveFormCorrige
          initialEleve={eleveToEdit}
          onSuccess={() => {
            setShowModal(false);
            fetchElevesByAnnee();
          }}
        />
      </Modal>

      {/* Modal de détails */}
      {eleveToView && (
        <EleveDetailModal
          eleve={eleveToView}
          isOpen={showDetailModal}
          onClose={handleCloseDetailModal}
        />
      )}

      <ConfirmationComponent />
    </div>
  );
}
