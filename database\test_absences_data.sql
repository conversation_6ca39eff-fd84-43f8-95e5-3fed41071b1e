-- Script de test pour les absences d'élèves
-- À exécuter pour créer des données de test

-- Vérifier la structure de la table absence_eleve
DESCRIBE absence_eleve;

-- Vérifier les élèves et classes existants
SELECT e.id_eleve, u.nom, u.prenom, e.code_massar, c.id_classe, c.nom_classe
FROM eleve e
JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
JOIN inscription i ON e.id_eleve = i.id_eleve
JOIN classe c ON i.id_classe = c.id_classe
JOIN annee_scolaire ans ON i.id_annee_scolaire = ans.id_annee_scolaire
WHERE ans.est_active = true
LIMIT 5;

-- Insérer quelques absences de test
-- Remplacez les ID par des valeurs réelles de votre base de données

-- Exemple d'absences (adaptez les ID selon votre base)
INSERT INTO absence_eleve (id_eleve, id_classe, date_debut, date_fin, duree, motif, justifiee, commentaire) VALUES
(1, 1, '2024-01-15 08:00:00', '2024-01-15 12:00:00', 240, 'maladie', 1, 'Grippe - <PERSON>rtificat médical fourni'),
(2, 1, '2024-01-16 09:00:00', '2024-01-16 10:30:00', 90, 'familial', 1, 'Rendez-vous médical'),
(3, 2, '2024-01-17 08:00:00', '2024-01-17 17:00:00', 540, 'maladie', 0, 'Absence non justifiée'),
(1, 1, '2024-01-18 14:00:00', '2024-01-18 17:00:00', 180, 'autre', 1, 'Départ anticipé autorisé');

-- Vérifier les données insérées
SELECT 
    ae.*,
    u.nom,
    u.prenom,
    e.code_massar,
    c.nom_classe
FROM absence_eleve ae
JOIN eleve e ON ae.id_eleve = e.id_eleve
JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
JOIN classe c ON ae.id_classe = c.id_classe
ORDER BY ae.date_debut DESC;

-- Tester la requête utilisée par l'API
SELECT 
    ae.*,
    u.nom,
    u.prenom,
    e.code_massar,
    c.nom_classe
FROM absence_eleve ae
JOIN eleve e ON ae.id_eleve = e.id_eleve
JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
JOIN classe c ON ae.id_classe = c.id_classe
WHERE 1=1
ORDER BY ae.date_debut DESC;

-- Tester les statistiques
SELECT 
    COUNT(*) as total_absences,
    COUNT(DISTINCT ae.id_eleve) as eleves_absents,
    SUM(CASE WHEN ae.justifiee = 1 THEN 1 ELSE 0 END) as absences_justifiees,
    SUM(CASE WHEN ae.justifiee = 0 THEN 1 ELSE 0 END) as absences_non_justifiees,
    AVG(ae.duree) as duree_moyenne,
    SUM(ae.duree) as duree_totale
FROM absence_eleve ae
WHERE 1=1;

-- Nettoyer les données de test (si nécessaire)
-- DELETE FROM absence_eleve WHERE commentaire LIKE '%test%' OR commentaire LIKE '%Grippe%';
