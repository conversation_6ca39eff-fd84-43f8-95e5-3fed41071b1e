export interface User {
    id_utilisateur: number;
    nom: string;
    prenom: string;
    email: string;
    mot_de_passe: string;
    role: 'admin' | 'enseignant' | 'eleve' | 'parent' | 'personnel';
    sexe: 'garçon' |'fille' |'homme'| 'femme' | null;
    date_naissance: string; 
    lieu_naissance: string;
    nationalite: string | null;
    telephone: string;
    adresse?: string;
    photo?: string;
    est_valide: boolean;
    est_actif: boolean;
    date_creation?: string;
    date_maj?: string;
  }
 
  export interface Parent {
    id_parent: number;
    id_utilisateur: number;
    nom_ar: string;
    prenom_ar: string;
    num_CIN: string;
    user?: User;
  }
  export interface Eleve {
    id_eleve: number;
    id_utilisateur: number;
    code_massar?: string;
    code_gresa?: string;
    nom_ar: string;
    prenom_ar: string;
    lieu_naissance_ar: string;
    user?: User;
    ancienne_ecole?: AncienneEcole;
    id_classe?: number;
    nom_classe?: string;
    id_niveau?: number;
  }
  export interface Relation {
    id_parent: number;
    id_eleve: number;
    type_relation: 'père' | 'mère' | 'tuteur' | null ;
  }
  
  export interface AncienneEcole{
    code_gresa: string,
    nom: string,
    type: 'publique' | 'privée',
    cycle: 'maternelle' | 'primaire' | 'collège' | 'lycée',
    adresse: string,
  }

  export interface Enseignant {
    id_enseignant: number;
    id_utilisateur: number;
    num_CIN: string;
    num_CNSS: string | null;
    situation_familiale: 'C' | 'M' | 'D' | 'V' | null;
    nombre_enfants: number | null;
    date_embauche: string;
    banque: string | null;
    rib: string | null;
    statut?: 'actif' | 'terminé' | 'suspendu';
    matieres?: Matiere[];
    classes?: Classe[];
    user?: User;
  }
 
  export interface Diplome {
    id_diplome: number;
    id_enseignant: number;
    intitule: string;
    institut: string;
    specialite: string;
    date_promotion: string;
  }
  export interface Contrat {
    id_contrat: number;
    id_enseignant: number;
    type_contrat: 'CDI' | 'CDD' | 'Vacataire' | 'Autre';
    poste: 'enseignant' | 'administratif' | 'autre';
    date_debut: string;
    date_fin?: string;
    salaire_base: number;
    statut: 'actif' | 'terminé' | 'suspendu';
    description?: string;
    matieres?: number[]; // IDs des matières pour les enseignants
  }

  export interface Niveau {
    id_niveau: number;
    libelle: string;
    cycle: 'primaire' | 'collège' | 'lycée';
    frais_inscription: number;
  }

  export interface Classe {
    id_classe: number;
    id_niveau: number;
    nom_classe: string;
    nombre_eleves?: number;
    niveau?: Niveau;
    niveau_libelle?: string;
    cycle?: string;
  }

  export interface Cours {
    id_cours: number;
    id_unite: number;
    id_enseignant: number;
    id_classe: number;
    id_salle: number;
    jour_semaine: 'Lundi' | 'Mardi' | 'Mercredi' | 'Jeudi' | 'Vendredi' | 'Samedi';
    heure_debut: string;
    heure_fin: string;
    nom_unite?: string;
    nom_matiere_fr?: string;
    enseignant_nom?: string;
    enseignant_prenom?: string;
    nom_salle?: string;
  }

  export interface Ecole {
    id_ecole: number;
    nom_ecole: string;
    adresse: string;
    telephone?: string;
    email?: string;
    site_web?: string;
    directeur?: string;
    logo?: string;
    type_etablissement: 'publique' | 'privée';
    cycles_proposes: ('maternelle' | 'primaire' | 'collège' | 'lycée')[];
    date_creation?: string;
    description?: string;
    created_at?: string;
    updated_at?: string;
  }

  export interface AnneeScolaire {
    id_annee_scolaire: number;
    libelle: string;
    date_debut: string;
    date_fin: string;
    est_active: boolean;
  }

  export interface NiveauDetaille {
    id_niveau: number;
    cycle: 'maternelle' | 'primaire' | 'collège' | 'lycée';
    libelle: string;
    prix_mensuel: number;
    frais_inscription: number;
  }

  export interface Paiement {
    id_paiement: number;
    id_eleve: number;
    id_annee_scolaire: number;
    montant: number;
    mois: 'Janvier' | 'Février' | 'Mars' | 'Avril' | 'Mai' | 'Juin' | 'Juillet' | 'Août' | 'Septembre' | 'Octobre' | 'Novembre' | 'Décembre';
    date_paiement: string;

    type_paiement: 'scolarité' | 'transport' | 'inscription' | 'activité' | 'autre';
    mode_paiement: 'espèces' | 'chèque' | 'virement' | 'carte';
    statut: 'en attente' | 'payé' | 'retard' | 'annulé';
    description?: string;
  }

  export interface StatistiquesPaiement {
    total_paiements: number;
    total_paye: number;
    total_en_attente: number;
    total_retard: number;
    nombre_payes: number;
    nombre_en_attente: number;
    nombre_retard: number;
  }

  export interface StatistiquesParType {
    type_paiement: string;
    nombre: number;
    total_montant: number;
    montant_paye: number;
  }

  export interface Echeancier {
    id_echeancier: number;
    id_eleve: number;
    id_annee_scolaire: number;
    type_paiement: string;
    montant_total: number;
    nombre_echeances: number;
    montant_echeance: number;
    date_premiere_echeance: string;
    statut: 'actif' | 'terminé' | 'suspendu';
    created_at?: string;
  }

  export interface DetailEcheance {
    id_detail_echeance: number;
    id_echeancier: number;
    numero_echeance: number;
    montant: number;
    date_echeance: string;
    statut: 'en_attente' | 'payé' | 'retard';
    id_paiement?: number;
  }

  export interface Ecole {
    id_ecole: number;
    nom_ecole: string;
    adresse: string;
    telephone?: string;
    email?: string;
    site_web?: string;
    directeur?: string;
    logo?: string;
    type_etablissement: 'publique' | 'privée';
    cycles_proposes: ('maternelle' | 'primaire' | 'collège' | 'lycée')[];
    date_creation?: string;
    description?: string;
    created_at?: string;
    updated_at?: string;
  }

  export interface AnneeScolaire {
    id_annee_scolaire: number;
    libelle: string;
    date_debut: string;
    date_fin: string;
    est_active: boolean;
  }

  export interface NiveauDetaille {
    id_niveau: number;
    cycle: 'maternelle' | 'primaire' | 'collège' | 'lycée';
    libelle: string;
    prix_mensuel: number;
    frais_inscription: number;
  }

  // export interface EleveData {
  //   eleve: Eleve & { email: string; mot_de_passe: string; telephone?: string };
  //   parent: Parent & { email: string; mot_de_passe: string; telephone?: string };
  //   scolaire: {
  //     code_gresa: string;
  //   };
  //   type_relation: 'père' | 'mère' | 'tuteur';
  // }
  // export interface EnseignantData {
  //   enseignant: Enseignant & { email: string; mot_de_passe: string; telephone?: string };
  //   diplomes: Diplome[];
  //   contrat: Contrat;
  // }

// Interfaces déjà définies plus haut, suppression des doublons

// Classe
export interface Classe {
  id_classe: number;
  id_niveau: number;
  nom_classe: string;
}

// Inscription
export interface Inscription {
  id_eleve: number;
  id_annee_scolaire: number;
  id_classe: number;
  date_inscription: string;
  statut: string;
}

// Matiere
export interface Matiere {
  id_matiere: number;
  nom_matiere_fr: string;
  nom_matiere_ar: string;
  description?: string;
}

// Unite
export interface Unite {
  id_unite: number;
  id_matiere: number;
  nom_unite: string;
  description?: string;
}

// Niveau_Matiere
export interface NiveauMatiere {
  id_niveau: number;
  id_matiere: number;
  coefficient: number;
  Nb_heures?: number;
}

// Salle
export interface Salle {
  id_salle: number;
  nom_salle: string;
  capacite: number;
}

// Cours
export interface Cours {
  id_cours: number;
  id_unite: number;
  id_enseignant: number;
  id_classe: number;
  id_salle: number;
  jour_semaine: 'Lundi' | 'Mardi' | 'Mercredi' | 'Jeudi' | 'Vendredi' | 'Samedi';
  heure_debut: string;
  heure_fin: string;
}

// Interface Examen supprimée car redéfinie plus bas avec plus de détails

// Interfaces supprimées car redéfinies plus bas avec plus de détails

// Interface AbsenceEleve supprimée - voir version complète plus bas

// Présence quotidienne
export interface PresenceQuotidienne {
  id_presence: number;
  id_eleve: number;
  id_classe: number;
  date_presence: string;
  present: boolean;
  retard_minutes: number;
  justifiee: boolean;
  motif?: string;
  commentaire?: string;
  saisi_par?: number;
  date_saisie?: string;
  // Données enrichies
  eleve?: {
    nom: string;
    prenom: string;
    code_massar: string;
  };
  classe?: {
    nom_classe: string;
  };
}

// Statistiques de présence
export interface StatistiquesPresence {
  total_eleves: number;
  presents_aujourdhui: number;
  absents_aujourdhui: number;
  retards_aujourdhui: number;
  taux_presence: number;
  taux_absence: number;
  absences_justifiees: number;
  absences_non_justifiees: number;
  moyenne_retard: number;
  evolution_presence: {
    date: string;
    presents: number;
    absents: number;
    retards: number;
  }[];
}

// Statistiques par élève
export interface StatistiquesPresenceEleve {
  id_eleve: number;
  nom: string;
  prenom: string;
  code_massar: string;
  classe: string;
  total_jours: number;
  jours_presents: number;
  jours_absents: number;
  jours_retard: number;
  taux_presence: number;
  taux_absence: number;
  absences_justifiees: number;
  absences_non_justifiees: number;
  total_minutes_retard: number;
  moyenne_retard: number;
}

// Paiement (interface mise à jour)
export interface Paiement {
  id_paiement: number;
  id_eleve: number;
  id_annee_scolaire: number;
  montant: number;
  mois: 'Janvier' | 'Février' | 'Mars' | 'Avril' | 'Mai' | 'Juin' | 'Juillet' | 'Août' | 'Septembre' | 'Octobre' | 'Novembre' | 'Décembre';
  annee: number;
  date_paiement: string;
  type_paiement: 'scolarité' | 'transport' | 'inscription' | 'activité' | 'autre';
  mode_paiement: 'espèces' | 'chèque' | 'virement' | 'carte';
  statut: 'en attente' | 'payé' | 'retard' | 'annulé';
  description?: string;
  numero_recu?: string;
  created_at?: string;
  updated_at?: string;
  eleve?: {
    nom: string;
    prenom: string;
    email: string;
    telephone?: string;
    classe?: string;
    niveau?: string;
    code_massar?: string;
    massar?: string; // Alias pour compatibilité
  };
  annee_scolaire?: string;
}

// Transport (interface basée sur la vraie structure DB)
export interface Transport {
  id_transport: number;
  trajet: string;
  matricule: string;
  prix: number;
  capacite: number;
  // Champs calculés ajoutés par le backend
  nombre_eleves_inscrits?: number;
  places_disponibles?: number;
  taux_occupation?: number;
}

// Inscription Transport (basée sur la table beneficier_transport)
export interface InscriptionTransport {
  id_eleve: number;
  id_transport: number;
  id_annee_scolaire: number;
  // Données enrichies par les jointures
  eleve?: {
    nom: string;
    prenom: string;
    code_massar: string;
    classe?: string;
  };
  transport?: {
    trajet: string;
    matricule: string;
    prix: number;
  };
  annee_scolaire?: string;
}

// ==================================== Gestion des Présences/Absences ====================================

// Absence Élève (basée sur la table absence_eleve)
export interface AbsenceEleve {
  id_absence_eleve: number;
  id_eleve: number;
  id_classe: number;
  date_debut: string;
  date_fin: string;
  duree?: number; // Calculé automatiquement en minutes
  motif: 'maladie' | 'familial' | 'autre';
  justifiee: boolean;
  commentaire?: string;
  // Données enrichies
  eleve?: {
    nom: string;
    prenom: string;
    code_massar: string;
  };
  classe?: {
    nom_classe: string;
  };
}

// Absence Enseignant (basée sur la table absence_enseignant)
export interface AbsenceEnseignant {
  id_absence_enseignant: number;
  id_enseignant: number;
  date_debut: string;
  date_fin: string;
  type: 'absence' | 'retard';
  justifiee: boolean;
  motif: 'maladie' | 'congé personnel' | 'formation' | 'autre';
  commentaire?: string;
  id_remplacant?: number;
  // Données enrichies
  enseignant?: {
    nom: string;
    prenom: string;
    specialite?: string;
  };
  remplacant?: {
    nom: string;
    prenom: string;
  };
}

// Statistiques des présences
export interface StatistiquesPresences {
  total_eleves: number;
  eleves_presents: number;
  eleves_absents: number;
  taux_presence: number;
  absences_justifiees: number;
  absences_non_justifiees: number;
  total_enseignants: number;
  enseignants_presents: number;
  enseignants_absents: number;
}

// Paiement Transport
export interface PaiementTransport {
  id_paiement_transport: number;
  id_inscription_transport: number;
  mois: 'Janvier' | 'Février' | 'Mars' | 'Avril' | 'Mai' | 'Juin' | 'Juillet' | 'Août' | 'Septembre' | 'Octobre' | 'Novembre' | 'Décembre';
  annee: number;
  montant: number;
  date_paiement: string;

  mode_paiement: 'espèces' | 'chèque' | 'virement' | 'carte';
  statut: 'en_attente' | 'payé' | 'retard' | 'annulé';
  numero_recu?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// Statistiques Transport
export interface StatistiquesTransport {
  total_transports: number;
  transports_actifs: number;
  transports_inactifs: number;
  capacite_totale: number;
  capacite_moyenne: number;
  prix_moyen: number;
  prix_min: number;
  prix_max: number;
}

// Statistiques Inscriptions Transport
export interface StatistiquesInscriptionsTransport {
  total_inscriptions: number;
  inscriptions_actives: number;
  inscriptions_suspendues: number;
  inscriptions_annulees: number;
  eleves_uniques: number;
  transports_utilises: number;
}

// ==================== EXAMENS ====================

// Examen
export interface Examen {
  id_examen: number;
  id_matiere: number;
  id_classe: number;
  id_annee_scolaire: number;
  id_enseignant?: number;
  titre: string;
  type_examen: 'examen' | 'devoir' | 'controle' | 'participation' | 'oral' | 'tp';
  date_examen: string;
  heure_debut?: string;
  heure_fin?: string;
  duree_examen: number; // en minutes
  semestre: 'S1' | 'S2';
  coefficient: number;
  note_sur: number;
  salle?: string;
  consignes?: string;
  bareme?: string;
  statut: 'planifie' | 'en_cours' | 'termine' | 'annule';
  est_rattrapage: boolean;
  id_examen_original?: number;
  commentaire?: string;
  // Données enrichies
  matiere_nom?: string;
  nom_classe?: string;
  niveau_libelle?: string;
  annee_libelle?: string;
  enseignant_nom?: string;
  nombre_notes?: number;
  moyenne_classe?: number;
  note_min?: number;
  note_max?: number;
  nombre_absents?: number;
  created_at?: string;
  updated_at?: string;
}

// Note
export interface Note {
  id_note: number;
  id_eleve: number;
  id_examen: number;
  note?: number;
  note_sur: number;
  note_sur_20?: number;
  absent: boolean;
  excuse: boolean;
  commentaire?: string;
  date_saisie?: string;
  date_modification?: string;
  saisi_par?: number;
  // Données enrichies
  examen?: {
    titre: string;
    type_examen: string;
    date_examen: string;
    coefficient: number;
    note_sur: number;
  };
  matiere_nom?: string;
  nom_classe?: string;
  eleve?: {
    nom: string;
    code_massar: string;
  };
  enseignant_nom?: string;
}

// Session d'examen
export interface SessionExamen {
  id_session: number;
  nom_session: string;
  id_annee_scolaire: number;
  semestre: 'S1' | 'S2';
  date_debut: string;
  date_fin: string;
  type_session: 'normale' | 'rattrapage' | 'exceptionnelle';
  description?: string;
  statut: 'planifiee' | 'en_cours' | 'terminee' | 'annulee';
  created_at?: string;
}

// Bulletin
export interface Bulletin {
  id_bulletin: number;
  id_eleve: number;
  id_annee_scolaire: number;
  id_classe: number;
  semestre: 'S1' | 'S2';
  moyenne_generale?: number;
  rang_classe?: number;
  total_eleves_classe?: number;
  appreciation_generale?: string;
  decision: 'admis' | 'redouble' | 'exclu' | 'en_attente';
  date_conseil_classe?: string;
  valide: boolean;
  date_creation?: string;
  date_modification?: string;
  cree_par?: number;
}

// Bulletin par matière
export interface BulletinMatiere {
  id_bulletin_matiere: number;
  id_bulletin: number;
  id_matiere: number;
  moyenne_matiere?: number;
  coefficient: number;
  nombre_notes: number;
  note_min?: number;
  note_max?: number;
  rang_matiere?: number;
  appreciation?: string;
}

// Statistiques examens
export interface StatistiquesExamens {
  total_examens: number;
  examens_planifies: number;
  examens_en_cours: number;
  examens_termines: number;
  examens_annules: number;
  examens_officiels: number;
  controles: number;
  devoirs: number;
  coefficient_moyen: number;
  matieres_evaluees: number;
  classes_concernees: number;
}

// Statistiques notes
export interface StatistiquesNotes {
  total_notes: number;
  notes_presentes: number;
  notes_absentes: number;
  moyenne_generale: number;
  note_min: number;
  note_max: number;
  notes_reussies: number;
  notes_echec: number;
}

// ==================== EMPLOI DU TEMPS ====================

// Cours
export interface Cours {
  id_cours: number;
  id_matiere: number;
  id_enseignant: number;
  id_classe: number;
  id_salle: number;
  id_annee_scolaire: number;
  jour_semaine: 'Lundi' | 'Mardi' | 'Mercredi' | 'Jeudi' | 'Vendredi' | 'Samedi';
  heure_debut: string;
  heure_fin: string;
  type_cours: 'cours' | 'td' | 'tp' | 'controle' | 'examen' | 'rattrapage';
  semestre: 'S1' | 'S2';
  duree_minutes: number;
  couleur: string;
  description?: string;
  est_active: boolean;
  // Données enrichies
  matiere_nom?: string;
  enseignant_nom?: string;
  nom_classe?: string;
  niveau_libelle?: string;
  nom_salle?: string;
  type_salle?: string;
  salle_capacite?: number;
  annee_libelle?: string;
  created_at?: string;
  updated_at?: string;
}

// Salle
export interface Salle {
  id_salle: number;
  nom_salle: string;
  capacite: number;
  type_salle: 'classe' | 'laboratoire' | 'informatique' | 'sport' | 'amphitheatre' | 'autre';
  equipements?: string;
  localisation?: string;
  est_active: boolean;
  // Données enrichies
  nombre_cours_semaine?: number;
  taux_occupation?: number;
  created_at?: string;
  updated_at?: string;
}

// Créneau horaire
export interface CreneauHoraire {
  id_creneau: number;
  nom_creneau: string;
  heure_debut: string;
  heure_fin: string;
  ordre_creneau: number;
  est_actif: boolean;
  created_at?: string;
}

// Exception de cours
export interface ExceptionCours {
  id_exception: number;
  id_cours: number;
  date_exception: string;
  type_exception: 'annule' | 'reporte' | 'remplace' | 'salle_changee';
  nouveau_id_salle?: number;
  nouveau_id_enseignant?: number;
  nouvelle_heure_debut?: string;
  nouvelle_heure_fin?: string;
  motif?: string;
  created_at?: string;
  created_by?: number;
}

// Absence de cours
export interface AbsenceCours {
  id_absence_cours: number;
  id_cours: number;
  id_eleve: number;
  date_cours: string;
  present: boolean;
  retard_minutes: number;
  justifiee: boolean;
  motif?: string;
  date_saisie?: string;
  saisi_par?: number;
}

// Statistiques cours
export interface StatistiquesCours {
  total_cours: number;
  cours_actifs: number;
  cours_normaux: number;
  td: number;
  tp: number;
  matieres_enseignees: number;
  enseignants_actifs: number;
  classes_concernees: number;
  salles_utilisees: number;
  duree_moyenne: number;
}

// Statistiques salles
export interface StatistiquesSalles {
  total_salles: number;
  salles_actives: number;
  salles_classe: number;
  laboratoires: number;
  salles_informatique: number;
  salles_sport: number;
  capacite_totale: number;
  capacite_moyenne: number;
  capacite_min: number;
  capacite_max: number;
}

// Emploi du temps (vue d'ensemble)
export interface EmploiTemps {
  id_classe?: number;
  id_enseignant?: number;
  id_salle?: number;
  nom_classe?: string;
  enseignant_nom?: string;
  nom_salle?: string;
  semestre: 'S1' | 'S2';
  cours: Cours[];
}

// Données pour l'affichage en grille
export interface EmploiTempsGrille {
  [jour: string]: {
    [heure: string]: Cours | null;
  };
}

// Conflit d'emploi du temps
export interface ConflitEmploiTemps {
  type: 'salle' | 'enseignant' | 'classe';
  cours1: Cours;
  cours2: Cours;
  message: string;
}

// Activite
export interface Activite {
  id_activite: number;
  id_annee_scolaire?: number;
  nom_activite: string;
  type_activite: 'sportive' | 'culturelle' | 'autre';
  date_debut?: string;
  date_fin?: string;
  heure_debut?: string;
  heure_fin?: string;
  jour_semaine?: string;
  lieu?: string;
  prix: number;
  capacite_max?: number;
  description?: string;
  id_responsable?: number;
  statut: 'active' | 'inactive' | 'terminee';
  // Données enrichies du backend
  responsable?: {
    nom: string;
    prenom: string;
  };
  nom_responsable?: string;
  prenom_responsable?: string;
  annee_scolaire?: string;
  nombre_inscrits?: number;
  places_restantes?: number;
}

// Participation à une activité
export interface ParticipationActivite {
  id_participation: number;
  id_eleve: number;
  id_activite: number;
  date_inscription: string;
  statut_participation: 'inscrit' | 'confirme' | 'present' | 'absent' | 'annule';
  commentaire?: string;
  note_evaluation?: number;
  created_at?: string;
  // Données enrichies
  eleve?: {
    nom: string;
    prenom: string;
    code_massar: string;
    classe: string;
  };
  activite?: {
    nom_activite: string;
    type_activite: string;
    date_debut: string;
  };
}

// Participant à une activité (vue simplifiée)
export interface ParticipantActivite {
  id_eleve: number;
  nom: string;
  prenom: string;
  code_massar: string;
  nom_classe: string;
  statut_participation: 'inscrit' | 'confirme' | 'present' | 'absent' | 'annule';
  date_inscription: string;
  note_evaluation?: number;
}

// Statistiques des activités
export interface StatistiquesActivites {
  total_activites: number;
  activites_planifiees: number;
  activites_en_cours: number;
  activites_terminees: number;
  activites_annulees: number;
  total_participants: number;
  taux_participation: number;
  activites_par_type: {
    type: string;
    nombre: number;
    participants: number;
  }[];
  evolution_mensuelle: {
    mois: string;
    activites: number;
    participants: number;
  }[];
  top_activites: {
    nom_activite: string;
    type_activite: string;
    participants: number;
    taux_satisfaction?: number;
  }[];
}

// Évaluation d'activité
export interface EvaluationActivite {
  id_evaluation: number;
  id_activite: number;
  id_eleve: number;
  note_satisfaction: number; // 1-5
  commentaire?: string;
  points_positifs?: string;
  points_amelioration?: string;
  recommande: boolean;
  date_evaluation: string;
  // Données enrichies
  eleve?: {
    nom: string;
    prenom: string;
  };
  activite?: {
    nom_activite: string;
  };
}

// Ressource d'activité
export interface RessourceActivite {
  id_ressource: number;
  id_activite: number;
  nom_ressource: string;
  type_ressource: 'materiel' | 'document' | 'lieu' | 'transport' | 'autre';
  quantite_requise?: number;
  quantite_disponible?: number;
  cout?: number;
  fournisseur?: string;
  statut: 'disponible' | 'reserve' | 'indisponible';
  description?: string;
}

// Participation_Activite
export interface ParticipationActivite {
  id_eleve: number;
  id_activite: number;
}

// Classe_Activite
export interface ClasseActivite {
  id_classe: number;
  id_activite: number;
}

// Message
export interface Message {
  id_message: number;
  id_emetteur: number;
  id_recepteur: number;
  objet: string;
  contenu: string;
  date_envoi: string;
  lu: boolean;
}

// Enseignant_Activite
export interface EnseignantActivite {
  id_enseignant: number;
  id_activite: number;
}

// Enseignant_Matiere
export interface EnseignantMatiere {
  id_enseignant: number;
  id_matiere: number;
}

// Absence_Enseignant
export interface AbsenceEnseignant {
  id_absence_enseignant: number;
  id_enseignant: number;
  date_debut: string;
  date_fin: string;
  type: 'absence' | 'retard';
  justifiee: boolean;
  motif: 'maladie' | 'congé personnel' | 'formation' | 'autre';
  commentaire?: string;
}

// Paie_Enseignant
export interface PaieEnseignant {
  id_paie: number;
  id_enseignant: number;
  salaire_brut: number;
  total_cotisation?: number;
  salaire_net_imposable: number;
  IGR: number;
  salaire_net_paye: number;
  mois_paie: string;
  annee: number;
  date_paie: string;
  mode_paie: 'virement' | 'chèque' | 'espèces';
  statut: 'en attente' | 'validé' | 'payé';
}




// Type_Rubrique
export interface TypeRubrique {
  code_rubrique: number;
  libelle: string;
  type_rubrique: 'gain' | 'retenue';
  imposable: boolean;
  formule_calcul: string;
}

// Rubrique_Paie
export interface RubriquePaie {
  id_rubrique_paie: number;
  id_paie: number;
  code_rubrique: number;
  montant: number;
}

// ==================== GESTION DES EXAMENS ====================

// Examen (nouvelle interface simplifiée)
export interface ExamenSimple {
  id_examen: number;
  id_matiere: number;
  id_classe: number;
  id_enseignant?: number;
  type_examen: 'examen' | 'devoir' | 'contrôle' | 'participation';
  date_examen: string;
  duree_examen: number; // En minutes
  semestre: 'S1' | 'S2';
  commentaire?: string;
  // Données enrichies
  matiere?: {
    nom_matiere_fr: string;
    nom_matiere_ar: string;
  };
  classe?: {
    nom_classe: string;
  };
  enseignant?: {
    nom: string;
    prenom: string;
  };
  nombre_notes?: number;
  moyenne_classe?: number;
}

// Note (nouvelle interface simplifiée)
export interface NoteSimple {
  id_eleve: number;
  id_examen: number;
  note: number; // Entre 0 et 20
  // Données enrichies
  eleve?: {
    nom: string;
    prenom: string;
    code_massar: string;
  };
  examen?: ExamenSimple;
}

// ==================== GESTION DES BULLETINS ====================

// Bulletin de notes
export interface BulletinData {
  eleve: {
    id_eleve: number;
    nom: string;
    prenom: string;
    code_massar: string;
    date_naissance: string;
    lieu_naissance: string;
    sexe: string;
    classe: string;
  };
  notes: {
    [matiere: string]: {
      contrôle?: number;
      devoir?: number;
      examen?: number;
      participation?: number;
      moyenne: number;
    };
  };
  moyennes: {
    moyenne_generale: number;
    nombre_matieres: number;
  };
  semestre: string;
  annee_scolaire: {
    id_annee_scolaire: number;
    nom_annee: string;
    date_debut: string;
    date_fin: string;
  };
}

// Bulletin résumé pour liste
export interface BulletinResume {
  id_eleve: number;
  nom: string;
  prenom: string;
  code_massar: string;
  moyenne_generale: number;
  bulletin_data?: BulletinData;
}

// Statistiques de classe
export interface StatistiquesClasse {
  nombre_eleves: number;
  moyenne_classe: number;
  moyenne_max: number;
  moyenne_min: number;
  nombre_admis: number;
  taux_reussite: number;
}

