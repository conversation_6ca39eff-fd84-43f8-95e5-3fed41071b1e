<?php
// models/Transport.php

class Transport {
    private $pdo;
    private $table = "transport";

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Récupérer tous les transports
    public function getAll() {
        try {
            $query = "
                SELECT 
                    t.*,
                    COUNT(DISTINCT bt.id_eleve) as nombre_eleves_inscrits,
                    (t.capacite - COUNT(DISTINCT bt.id_eleve)) as places_disponibles,
                    ROUND((COUNT(DISTINCT bt.id_eleve) / t.capacite) * 100, 2) as taux_occupation
                FROM {$this->table} t
                LEFT JOIN beneficier_transport bt ON t.id_transport = bt.id_transport
                LEFT JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire AND ans.est_active = true
                GROUP BY t.id_transport
                ORDER BY t.trajet
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer un transport par ID
    public function getById($id) {
        try {
            $query = "
                SELECT 
                    t.*,
                    COUNT(DISTINCT bt.id_eleve) as nombre_eleves_inscrits,
                    (t.capacite - COUNT(DISTINCT bt.id_eleve)) as places_disponibles,
                    ROUND((COUNT(DISTINCT bt.id_eleve) / t.capacite) * 100, 2) as taux_occupation
                FROM {$this->table} t
                LEFT JOIN beneficier_transport bt ON t.id_transport = bt.id_transport
                LEFT JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire AND ans.est_active = true
                WHERE t.id_transport = ?
                GROUP BY t.id_transport
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    // Créer un nouveau transport
    public function create($data) {
        try {
            $query = "
                INSERT INTO {$this->table} (trajet, matricule, prix, capacite)
                VALUES (?, ?, ?, ?)
            ";
            
            $stmt = $this->pdo->prepare($query);
            $result = $stmt->execute([
                $data['trajet'],
                $data['matricule'],
                $data['prix'],
                $data['capacite'] ?? 25
            ]);
            
            if ($result) {
                return $this->pdo->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Erreur dans create: " . $e->getMessage());
            return false;
        }
    }

    // Mettre à jour un transport
    public function update($id, $data) {
        try {
            $query = "
                UPDATE {$this->table} SET 
                    trajet = ?, matricule = ?, prix = ?, capacite = ?
                WHERE id_transport = ?
            ";
            
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([
                $data['trajet'],
                $data['matricule'],
                $data['prix'],
                $data['capacite'],
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans update: " . $e->getMessage());
            return false;
        }
    }

    // Supprimer un transport
    public function delete($id) {
        try {
            // Vérifier s'il y a des élèves inscrits
            $checkQuery = "SELECT COUNT(*) FROM beneficier_transport WHERE id_transport = ?";
            $checkStmt = $this->pdo->prepare($checkQuery);
            $checkStmt->execute([$id]);
            
            if ($checkStmt->fetchColumn() > 0) {
                return ['error' => 'Impossible de supprimer ce transport car des élèves y sont inscrits'];
            }

            $query = "DELETE FROM {$this->table} WHERE id_transport = ?";
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les élèves inscrits à un transport
    public function getElevesInscrits($id_transport) {
        try {
            $query = "
                SELECT 
                    e.id_eleve,
                    u.nom,
                    u.prenom,
                    e.code_massar,
                    c.nom_classe,
                    bt.id_annee_scolaire,
                    ans.libelle as annee_scolaire
                FROM beneficier_transport bt
                JOIN eleve e ON bt.id_eleve = e.id_eleve
                JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN inscription i ON e.id_eleve = i.id_eleve AND i.id_annee_scolaire = bt.id_annee_scolaire
                JOIN classe c ON i.id_classe = c.id_classe
                JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
                WHERE bt.id_transport = ? AND ans.est_active = true
                ORDER BY u.nom, u.prenom
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_transport]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getElevesInscrits: " . $e->getMessage());
            return false;
        }
    }

    // Inscrire un élève à un transport
    public function inscrireEleve($id_transport, $id_eleve) {
        try {
            // Récupérer l'année scolaire active
            $anneeQuery = "SELECT id_annee_scolaire FROM annee_scolaire WHERE est_active = true LIMIT 1";
            $anneeStmt = $this->pdo->prepare($anneeQuery);
            $anneeStmt->execute();
            $annee = $anneeStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$annee) {
                return ['error' => 'Aucune année scolaire active trouvée'];
            }

            // Vérifier si déjà inscrit
            $checkQuery = "SELECT COUNT(*) FROM beneficier_transport WHERE id_transport = ? AND id_eleve = ? AND id_annee_scolaire = ?";
            $checkStmt = $this->pdo->prepare($checkQuery);
            $checkStmt->execute([$id_transport, $id_eleve, $annee['id_annee_scolaire']]);
            
            if ($checkStmt->fetchColumn() > 0) {
                return ['error' => 'Élève déjà inscrit à ce transport'];
            }

            // Vérifier la capacité
            $transport = $this->getById($id_transport);
            if ($transport && $transport['nombre_eleves_inscrits'] >= $transport['capacite']) {
                return ['error' => 'Transport complet'];
            }

            $query = "
                INSERT INTO beneficier_transport (id_transport, id_eleve, id_annee_scolaire)
                VALUES (?, ?, ?)
            ";
            
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id_transport, $id_eleve, $annee['id_annee_scolaire']]);
        } catch (PDOException $e) {
            error_log("Erreur dans inscrireEleve: " . $e->getMessage());
            return false;
        }
    }

    // Désinscrire un élève d'un transport
    public function desinscrireEleve($id_transport, $id_eleve) {
        try {
            // Récupérer l'année scolaire active
            $anneeQuery = "SELECT id_annee_scolaire FROM annee_scolaire WHERE est_active = true LIMIT 1";
            $anneeStmt = $this->pdo->prepare($anneeQuery);
            $anneeStmt->execute();
            $annee = $anneeStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$annee) {
                return ['error' => 'Aucune année scolaire active trouvée'];
            }

            $query = "DELETE FROM beneficier_transport WHERE id_transport = ? AND id_eleve = ? AND id_annee_scolaire = ?";
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id_transport, $id_eleve, $annee['id_annee_scolaire']]);
        } catch (PDOException $e) {
            error_log("Erreur dans desinscrireEleve: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les statistiques des transports
    public function getStatistiques() {
        try {
            $query = "
                SELECT 
                    COUNT(*) as total_transports,
                    SUM(t.capacite) as capacite_totale,
                    AVG(t.capacite) as capacite_moyenne,
                    AVG(t.prix) as prix_moyen,
                    MIN(t.prix) as prix_min,
                    MAX(t.prix) as prix_max,
                    SUM(CASE WHEN bt.nombre_eleves > 0 THEN 1 ELSE 0 END) as transports_utilises,
                    SUM(COALESCE(bt.nombre_eleves, 0)) as total_eleves_inscrits
                FROM {$this->table} t
                LEFT JOIN (
                    SELECT 
                        bt.id_transport,
                        COUNT(bt.id_eleve) as nombre_eleves
                    FROM beneficier_transport bt
                    JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
                    WHERE ans.est_active = true
                    GROUP BY bt.id_transport
                ) bt ON t.id_transport = bt.id_transport
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getStatistiques: " . $e->getMessage());
            return false;
        }
    }

    // Vérifier si un matricule existe déjà
    public function matriculeExists($matricule, $excludeId = null) {
        try {
            $query = "SELECT COUNT(*) FROM {$this->table} WHERE matricule = ?";
            $params = [$matricule];
            
            if ($excludeId) {
                $query .= " AND id_transport != ?";
                $params[] = $excludeId;
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Erreur dans matriculeExists: " . $e->getMessage());
            return false;
        }
    }
}
?>
