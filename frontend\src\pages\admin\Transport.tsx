import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Bus, Users, DollarSign, BarChart3, Edit, Trash2, Eye } from 'lucide-react';
import Button from '../../components/Button';
import Input from '../../components/Input';
import Select from '../../components/Select';
import Table from '../../components/Table';
import { 
  getTransports, 
  deleteTransport, 
  getStatistiquesTransports 
} from '../../services/api';
import { Transport } from '../../types';
import TransportForm from '../../components/TransportForm';
import TransportDetailsModal from '../../components/TransportDetailsModal';
import Pagination from '../../components/Pagination';
import { usePagination } from '../../hooks/usePagination';

const TransportPage: React.FC = () => {
  const [transports, setTransports] = useState<Transport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedTransport, setSelectedTransport] = useState<Transport | null>(null);
  const [statistics, setStatistics] = useState<any>(null);

  // Filtres
  const [capaciteFilter, setCapaciteFilter] = useState('');
  const [prixFilter, setPrixFilter] = useState('');
  const [occupationFilter, setOccupationFilter] = useState('');

  useEffect(() => {
    fetchTransports();
    fetchStatistics();
  }, []);

  const fetchTransports = async () => {
    try {
      setLoading(true);
      const response = await getTransports();
      setTransports(response.data.data || []);
      setError(null);
    } catch (error: any) {
      console.error('Erreur lors du chargement des transports:', error);
      setError('Erreur lors du chargement des transports');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await getStatistiquesTransports();
      setStatistics(response.data.data || {});
    } catch (error: any) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  const handleDelete = async (id: number, trajet: string) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le transport "${trajet}" ?`)) {
      try {
        await deleteTransport(id);
        setSuccessMessage('Transport supprimé avec succès');
        await fetchTransports();
        await fetchStatistics();
        
        setTimeout(() => setSuccessMessage(null), 3000);
      } catch (error: any) {
        console.error('Erreur lors de la suppression:', error);
        setError(error.response?.data?.message || 'Erreur lors de la suppression du transport');
      }
    }
  };

  const handleEdit = (transport: Transport) => {
    setSelectedTransport(transport);
    setShowForm(true);
  };

  const handleShowDetails = (transport: Transport) => {
    setSelectedTransport(transport);
    setShowDetails(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setSelectedTransport(null);
    fetchTransports();
    fetchStatistics();
  };

  const resetFilters = () => {
    setSearchTerm('');
    setCapaciteFilter('');
    setPrixFilter('');
    setOccupationFilter('');
  };

  // Filtrage des transports
  const filteredTransports = transports.filter(transport => {
    const matchesSearch = 
      transport.trajet.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transport.matricule.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCapacite = !capaciteFilter || 
      (capaciteFilter === 'small' && transport.capacite <= 20) ||
      (capaciteFilter === 'medium' && transport.capacite > 20 && transport.capacite <= 40) ||
      (capaciteFilter === 'large' && transport.capacite > 40);

    const matchesPrix = !prixFilter ||
      (prixFilter === 'low' && transport.prix <= 100) ||
      (prixFilter === 'medium' && transport.prix > 100 && transport.prix <= 200) ||
      (prixFilter === 'high' && transport.prix > 200);

    const matchesOccupation = !occupationFilter ||
      (occupationFilter === 'low' && (transport.taux_occupation || 0) <= 50) ||
      (occupationFilter === 'medium' && (transport.taux_occupation || 0) > 50 && (transport.taux_occupation || 0) <= 80) ||
      (occupationFilter === 'high' && (transport.taux_occupation || 0) > 80);

    return matchesSearch && matchesCapacite && matchesPrix && matchesOccupation;
  });

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedTransports,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredTransports,
    initialItemsPerPage: 10
  });

  // Configuration des colonnes pour le tableau (format Élèves)
  const columns = [
    { header: "Transport", accessor: "transport", className: "p-3" },
    { header: "Capacité", accessor: "capacite", className: "p-3" },
    { header: "Prix", accessor: "prix", className: "p-3" },
    { header: "Occupation", accessor: "occupation", className: "p-3" },
    { header: "Actions", accessor: "actions", className: "p-3" },
  ];

  const renderRow = (transport: Transport) => (
    <tr key={transport.id_transport} className="border-b even:bg-gray-50 hover:bg-gray-50">
      <td className="p-3">
        <div className="text-sm font-medium text-gray-900">
          {transport.trajet}
        </div>
        <div className="text-sm text-gray-500">
          {transport.matricule}
        </div>
      </td>
      <td className="p-3">
        <span className="text-sm text-gray-600">
          {transport.nombre_eleves_inscrits || 0}/{transport.capacite}
        </span>
      </td>
      <td className="p-3">
        <span className="text-sm font-medium text-gray-900">
          {formatPrix(transport.prix)}
        </span>
      </td>
      <td className="p-3">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getOccupationColor(transport.taux_occupation || 0)}`}>
          {Math.round(transport.taux_occupation || 0)}%
        </span>
      </td>
      <td className="p-3">
        <div className="flex space-x-2">
          <button
            className="text-blue-600 hover:text-blue-900"
            onClick={() => handleShowDetails(transport)}
            title="Voir les détails"
          >
            <Eye size={16} />
          </button>
          <button
            className="text-indigo-600 hover:text-indigo-900"
            onClick={() => handleEdit(transport)}
            title="Modifier"
          >
            <Edit size={16} />
          </button>
          <button
            className="text-red-600 hover:text-red-900"
            onClick={() => handleDelete(transport.id_transport, transport.trajet)}
            title="Supprimer"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </td>
    </tr>
  );

  const capaciteOptions = [
    { value: '', label: 'Toutes les capacités' },
    { value: 'small', label: 'Petite (≤ 20)' },
    { value: 'medium', label: 'Moyenne (21-40)' },
    { value: 'large', label: 'Grande (> 40)' }
  ];

  const prixOptions = [
    { value: '', label: 'Tous les prix' },
    { value: 'low', label: 'Économique (≤ 100 DH)' },
    { value: 'medium', label: 'Standard (101-200 DH)' },
    { value: 'high', label: 'Premium (> 200 DH)' }
  ];

  const occupationOptions = [
    { value: '', label: 'Tous les taux' },
    { value: 'low', label: 'Faible (≤ 50%)' },
    { value: 'medium', label: 'Moyen (51-80%)' },
    { value: 'high', label: 'Élevé (> 80%)' }
  ];

  const formatPrix = (prix: number) => {
    return `${prix.toLocaleString()} DH`;
  };

  const getOccupationColor = (taux: number) => {
    if (taux <= 50) return 'text-green-600 bg-green-100';
    if (taux <= 80) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <Bus className="w-8 h-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Transports</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.total_transports || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Élèves Inscrits</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.total_eleves_inscrits || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Prix Moyen</p>
                <p className="text-2xl font-bold text-gray-900">
                  {statistics.prix_moyen ? `${Math.round(statistics.prix_moyen)} DH` : '0 DH'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <BarChart3 className="w-8 h-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Capacité Totale</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.capacite_totale || 0}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Header avec recherche et boutons */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
             
        <Input
          placeholder="Rechercher par trajet ou matricule..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          leftIcon={<Search size={16} />}
        />
        <div className="flex gap-2">
          <Button
            variant="outline"
            icon={<Filter size={16} />}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filtres
          </Button>
          <Button
            icon={<Plus size={16} />}
            onClick={() => setShowForm(true)}
            variant="primary"
          >
            Nouveau Transport
          </Button>
        </div>
      </div>

      
      {/* Filtres et recherche */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        {/* Filtres avancés */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select
                label="Capacité"
                value={capaciteFilter}
                onChange={(e) => setCapaciteFilter(e.target.value)}
                options={capaciteOptions}
              />
              
              <Select
                label="Prix"
                value={prixFilter}
                onChange={(e) => setPrixFilter(e.target.value)}
                options={prixOptions}
              />
              
              <Select
                label="Taux d'occupation"
                value={occupationFilter}
                onChange={(e) => setOccupationFilter(e.target.value)}
                options={occupationOptions}
              />
            </div>
          </div>
        )}
        {(searchTerm || capaciteFilter || prixFilter || occupationFilter) && (
              <Button
                variant="outline"
                onClick={resetFilters}
              >
                Réinitialiser
              </Button>
        )}
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
          {successMessage}
        </div>
      )}
      {/* Liste des transports */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="ml-2">Chargement...</span>
          </div>
        ) : paginatedTransports.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Bus className="mx-auto mb-2 w-10 h-10" />
            <p>Aucun transport trouvé</p>
            {(searchTerm || capaciteFilter || prixFilter || occupationFilter) && (
              <p className="text-sm">
                Essayez de modifier votre recherche ou{" "}
                <button onClick={() => {
                  setSearchTerm('');
                  setCapaciteFilter('');
                  setPrixFilter('');
                  setOccupationFilter('');
                }} className="text-blue-600 underline">
                  effacer les filtres
                </button>
              </p>
            )}
          </div>
        ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <Table columns={columns} data={paginatedTransports} renderRow={renderRow} />
          {/* Pagination */}
          {filteredTransports.length > 0 && (
            <div className="px-6 py-3 border-gray-200 text-sm">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={setCurrentPage}
                onItemsPerPageChange={setItemsPerPage}
                showItemsPerPage={true}
              />
            </div>
          )}
        </div>
        )}
      </div>

      {/* Modals */}
      {showForm && (
        <TransportForm
          transport={selectedTransport}
          onClose={() => {
            setShowForm(false);
            setSelectedTransport(null);
          }}
          onSuccess={handleFormSuccess}
        />
      )}

      {showDetails && selectedTransport && (
        <TransportDetailsModal
          transport={selectedTransport}
          onClose={() => {
            setShowDetails(false);
            setSelectedTransport(null);
          }}
          onRefresh={() => {
            fetchTransports();
            fetchStatistics();
          }}
        />
      )}
    </div>
  );
};

export default TransportPage;
