import React from 'react';
import { useNavigate } from 'react-router-dom';
import {GraduationCap, Users, BookOpen, Award, MapPin, Phone, Mail, Clock, Star, ArrowRight, CheckCircle} from 'lucide-react';
import Button from '../components/Button';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <GraduationCap className="w-8 h-8 text-blue-600" />,
      title: "Excellence Académique",
      description: "Un enseignement de qualité avec des programmes adaptés à chaque niveau"
    },
    {
      icon: <Users className="w-8 h-8 text-green-600" />,
      title: "<PERSON><PERSON><PERSON>édagogi<PERSON>",
      description: "Des enseignants qualifiés et expérimentés pour accompagner vos enfants"
    },
    {
      icon: <BookOpen className="w-8 h-8 text-purple-600" />,
      title: "Programmes Complets",
      description: "De la maternelle au lycée, des cursus complets et enrichissants"
    },
    {
      icon: <Award className="w-8 h-8 text-orange-600" />,
      title: "Résultats Exceptionnels",
      description: "Des taux de réussite élevés et une préparation optimale aux examens"
    }
  ];

  const stats = [
    { number: "500+", label: "Élèves" },
    { number: "25+", label: "Enseignants" },
    { number: "15+", label: "Années d'expérience" },
    { number: "95%", label: "Taux de réussite" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 text-center">ScolaNova</h1>
                <p className="text-sm text-gray-600">Excellence et Innovation</p>
              </div>
            </div>
            <Button
              onClick={() => navigate('/login')}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Se connecter
              <ArrowRight size={16} />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Bienvenue à l'École
              <span className="text-blue-600 block">ScolaNova</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Une institution d'excellence dédiée à l'épanouissement et à la réussite de chaque élève. 
              Nous offrons un environnement d'apprentissage moderne et bienveillant.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => navigate('/login')}
                className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-lg font-medium transition-colors"
              >
                Accéder à l'espace personnel
              </Button>
              <Button
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                variant="outline"
                className="px-8 py-3 border-2 border-blue-600 text-blue-600 hover:bg-blue-50 rounded-lg text-lg font-medium transition-colors"
              >
                Nous contacter
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Pourquoi choisir ScolaNova ?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez les avantages qui font de notre école un choix d'excellence pour l'éducation de vos enfants.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div className="mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Nos Services
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                ScolaNova offre une gamme complète de services pour accompagner la réussite scolaire de vos enfants.
              </p>
              
              <div className="space-y-4">
                {[
                  "Enseignement multilingue (Français, Arabe, Anglais)",
                  "Activités parascolaires variées",
                  "Transport scolaire sécurisé",
                  "Cantine avec repas équilibrés",
                  "Suivi personnalisé de chaque élève",
                  "Préparation aux examens officiels"
                ].map((service, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{service}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-100 to-purple-100 p-8 rounded-2xl">
              <div className="text-center">
                <Star className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Excellence Reconnue
                </h3>
                <p className="text-gray-700 mb-6">
                  Notre école est reconnue pour la qualité de son enseignement et l'accompagnement personnalisé de chaque élève.
                </p>
                <div className="bg-white p-4 rounded-lg">
                  <div className="text-3xl font-bold text-blue-600">95%</div>
                  <div className="text-sm text-gray-600">Taux de réussite aux examens</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Contactez-nous
            </h2>
            <p className="text-xl text-gray-300">
              N'hésitez pas à nous contacter pour plus d'informations
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <MapPin className="w-8 h-8 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Adresse</h3>
              <p className="text-gray-300">
                123 Avenue de l'Éducation<br />
                Casablanca, Maroc
              </p>
            </div>
            
            <div className="text-center">
              <Phone className="w-8 h-8 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Téléphone</h3>
              <p className="text-gray-300">
                +212 5 22 XX XX XX<br />
                +212 6 XX XX XX XX
              </p>
            </div>
            
            <div className="text-center">
              <Mail className="w-8 h-8 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Email</h3>
              <p className="text-gray-300">
                <EMAIL><br />
                <EMAIL>
              </p>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <div className="flex items-center justify-center space-x-2 text-gray-300">
              <Clock className="w-5 h-5" />
              <span>Horaires : Lundi - Vendredi, 8h00 - 17h00</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-semibold">ÉcoleScolaNova</span>
            </div>
            <div className="text-gray-400 text-sm">
              © 2024 École ScolaNova. Tous droits réservés.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
