import React, { useState, useEffect } from "react";
import { Plus, Search, Filter, DollarSign, Clock, AlertTriangle, CheckCircle, Edit, Trash2, Calendar, RotateCcw } from "lucide-react";
import Button from "../../components/Button";
import Modal from "../../components/Modal";
import Pagination from "../../components/Pagination";
import { usePagination } from "../../hooks/usePagination";
import PaiementForm from "../../components/PaiementForm";
import GenerationPaiementsMensuels from "../../components/GenerationPaiementsMensuels";
import { getPaiements, deletePaiement, getStatistiquesPaiements, getAnneesScolaires, getClasses } from "../../services/api";
import type { Paiement, StatistiquesPaiement } from "../../types";
import { useDeleteConfirmation } from "../../hooks/useConfirmation";

const Paiements: React.FC = () => {
  const [paiements, setPaiements] = useState<Paiement[]>([]);
  const [filteredPaiements, setFilteredPaiements] = useState<Paiement[]>([]);

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedPaiements,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredPaiements,
    initialItemsPerPage: 15
  });
  const [statistiques, setStatistiques] = useState<StatistiquesPaiement | null>(null);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showGenerationModal, setShowGenerationModal] = useState(false);
  const [paiementToEdit, setPaiementToEdit] = useState<Paiement | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [anneeFilter, setAnneeFilter] = useState<string>("");
  const [moisFilter, setMoisFilter] = useState<string>("");
  const [classeFilter, setClasseFilter] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState("");
  const [anneesScolaires, setAnneesScolaires] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Hook pour la confirmation de suppression
  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  // Liste des mois pour le filtre
  const moisOptions = [
    "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
    "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"
  ];

  // Fonction pour extraire l'année à partir de l'année scolaire et du mois
  const getAnneeFromPaiement = (paiement: Paiement) => {
    // Si le paiement a déjà un champ annee, l'utiliser
    if ('annee' in paiement && paiement.annee) {
      return paiement.annee;
    }

    // Sinon, trouver l'année scolaire correspondante
    const anneeScolaire = anneesScolaires.find(
      (annee: any) => annee.id_annee_scolaire === paiement.id_annee_scolaire
    );

    if (anneeScolaire) {
      // Extraire les deux années du libellé (ex: "2024-2025" -> [2024, 2025])
      const match = anneeScolaire.libelle.match(/^(\d{4})-(\d{4})$/);
      if (match) {
        const premiereAnnee = parseInt(match[1]); // 2024
        const deuxiemeAnnee = parseInt(match[2]);  // 2025

        // Déterminer quelle année utiliser selon le mois
        const moisPremiereSemestre = [
          'Septembre', 'Octobre', 'Novembre', 'Décembre'
        ];
        const moisDeuxiemeSemestre = [
          'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin'
        ];

        if (moisPremiereSemestre.includes(paiement.mois)) {
          return premiereAnnee; // Septembre-Décembre -> première année
        } else if (moisDeuxiemeSemestre.includes(paiement.mois)) {
          return deuxiemeAnnee; // Janvier-Juin -> deuxième année
        } else {
          // Pour Juillet et Août (vacances), utiliser la première année par défaut
          return premiereAnnee;
        }
      }
    }

    // Par défaut, utiliser l'année actuelle
    return new Date().getFullYear();
  };

  useEffect(() => {
    fetchPaiements();
    fetchAnneesScolaires(); // Ceci chargera aussi les statistiques pour l'année active
    fetchClasses();

    // Sélectionner le mois actuel par défaut
    const currentMonth = moisOptions[new Date().getMonth()];
    setMoisFilter(currentMonth);
  }, []);

  // Appliquer les filtres quand les données ou filtres changent
  useEffect(() => {
    filterPaiements();
  }, [paiements, searchTerm, statusFilter, typeFilter, anneeFilter, moisFilter, classeFilter]);

  // Recharger les statistiques quand l'année scolaire change
  useEffect(() => {
    if (anneeFilter && anneesScolaires.length > 0) {
      const anneeSelectionnee = anneesScolaires.find((annee: any) => annee.libelle === anneeFilter);
      if (anneeSelectionnee) {
        fetchStatistiques(anneeSelectionnee.id_annee_scolaire);
      }
    }
  }, [anneeFilter, anneesScolaires]);

  const fetchPaiements = async () => {
    setLoading(true);
    try {
      const response = await getPaiements();
      if (response.data.success) {
        setPaiements(response.data.data);
      } else {
        setErrorMessage("Erreur lors du chargement des paiements");
      }
    } catch (error) {
      console.error("Erreur lors du chargement des paiements:", error);
      setErrorMessage("Erreur de connexion à l'API des paiements");
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistiques = async (anneeId?: number) => {
    try {
      const response = await getStatistiquesPaiements(anneeId);
      if (response.data.success) {
        setStatistiques(response.data.data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
    }
  };

  const fetchAnneesScolaires = async () => {
    try {
      const response = await getAnneesScolaires();
      if (response.data.success) {
        setAnneesScolaires(response.data.data);

        // Sélectionner l'année active par défaut
        const anneeActive = response.data.data.find((annee: any) => annee.est_active);
        if (anneeActive && !anneeFilter) {
          setAnneeFilter(anneeActive.libelle);
          // Charger les statistiques pour l'année active
          await fetchStatistiques(anneeActive.id_annee_scolaire);
        }
      }
    } catch (error) {
      console.error("Erreur lors du chargement des années scolaires:", error);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await getClasses();
      if (response.data.success) {
        setClasses(response.data.data);
      }
    } catch (error) {
      console.error("Erreur lors du chargement des classes:", error);
    }
  };

  const filterPaiements = () => {
    let filtered = paiements;

    // Filtre par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(paiement =>
        paiement.eleve?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        paiement.eleve?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        paiement.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par statut
    if (statusFilter) {
      filtered = filtered.filter(paiement => paiement.statut === statusFilter);
    }

    // Filtre par type
    if (typeFilter) {
      filtered = filtered.filter(paiement => paiement.type_paiement === typeFilter);
    }

    // Filtre par année scolaire
    if (anneeFilter) {
      filtered = filtered.filter(paiement => {
        return paiement.annee_scolaire === anneeFilter;
      });
    }

    // Filtre par mois
    if (moisFilter) {
      filtered = filtered.filter(paiement => paiement.mois === moisFilter);
    }

    // Filtre par classe
    if (classeFilter) {
      filtered = filtered.filter(paiement =>
        paiement.eleve?.classe === classeFilter
      );
    }

    setFilteredPaiements(filtered);
    resetPagination(); // Réinitialiser la pagination lors du filtrage
  };

  const handleAdd = () => {
    setPaiementToEdit(null);
    setShowModal(true);
  };

  const handleGeneration = () => {
    setShowGenerationModal(true);
  };

  const handleEdit = (paiement: Paiement) => {
    setPaiementToEdit(paiement);
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deletePaiement(id);
      if (response.data.success) {
        await fetchPaiements();
        // Recharger les statistiques pour l'année sélectionnée
        if (anneeFilter && anneesScolaires.length > 0) {
          const anneeSelectionnee = anneesScolaires.find((annee: any) => annee.libelle === anneeFilter);
          if (anneeSelectionnee) {
            await fetchStatistiques(anneeSelectionnee.id_annee_scolaire);
          }
        }
      } else {
        setErrorMessage("Erreur lors de la suppression du paiement");
      }
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
      setErrorMessage("Erreur lors de la suppression du paiement");
    }
  };

  const handleDeleteClick = (paiement: Paiement) => {
    const paiementName = `${paiement.type_paiement} - ${paiement.eleve?.prenom} ${paiement.eleve?.nom} (${paiement.montant}€)`;
    confirmDelete(
      () => handleDelete(paiement.id_paiement),
      paiementName,
      "Cette action supprimera définitivement ce paiement."
    );
  };

  const handleModalClose = () => {
    setShowModal(false);
    setPaiementToEdit(null);
  };

  const handlePaiementSubmit = async () => {
    setShowModal(false);
    setPaiementToEdit(null);
    await fetchPaiements();
    await fetchStatistiques();
  };

  const handleGenerationSubmit = async () => {
    setShowGenerationModal(false);
    await fetchPaiements();
    await fetchStatistiques();
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case 'payé':
        return 'bg-green-100 text-green-800';
      case 'en attente':
        return 'bg-yellow-100 text-yellow-800';
      case 'retard':
        return 'bg-red-100 text-red-800';
      case 'annule':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'scolarité':
        return 'bg-primary bg-opacity-10 text-primary';
      case 'transport':
        return 'bg-purple-100 text-purple-800';
      case 'inscription':
        return 'bg-green-100 text-green-800';
      case 'activité':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatMontant = (montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'MAD'
    }).format(montant);
  };



  const handleResetFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setTypeFilter("");
    setClasseFilter("");

    // Remettre l'année active par défaut
    const anneeActive = anneesScolaires.find((annee: any) => annee.est_active);
    if (anneeActive) {
      setAnneeFilter(anneeActive.libelle);
    } else {
      setAnneeFilter("");
    }

    // Remettre le mois actuel par défaut
    const currentMonth = moisOptions[new Date().getMonth()];
    setMoisFilter(currentMonth);
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        <span className="ml-2">Chargement des paiements...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">


      {/* Messages d'erreur */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {errorMessage}
        </div>
      )}

      {/* Statistiques */}
      {statistiques && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-primary" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total paiements</p>
                <p className="text-lg font-semibold text-gray-900">{formatMontant(statistiques.total_paiements)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Payés</p>
                <p className="text-lg font-semibold text-gray-900">{statistiques.nombre_payes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">En attente</p>
                <p className="text-lg font-semibold text-gray-900">{statistiques.nombre_en_attente}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">En retard</p>
                <p className="text-lg font-semibold text-gray-900">{statistiques.nombre_retard}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* En-tête avec recherche, filtres et boutons d'action */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="flex items-center gap-3">
          {/* Recherche */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Rechercher un élève..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            />
          </div>

          {/* Bouton Filtres */}
          <Button
            icon={<Filter size={16} />}
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filtres
          </Button>
        </div>

        <div className="flex space-x-3">
          <Button
            icon={<Calendar size={16} />}
            variant="outline"
            onClick={handleGeneration}
          >
            Générer paiements mensuels
          </Button>
          <Button
            icon={<Plus size={16} />}
            variant="primary"
            onClick={handleAdd}
          >
            Ajouter un paiement
          </Button>
        </div>
      </div>

      {/* Panneau de filtres */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex flex-wrap items-center gap-3 justify-start">
            {/* Filtre par statut */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="flex-1 min-w-[140px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les statuts</option>
              <option value="en attente">En attente</option>
              <option value="payé">Payé</option>
              <option value="retard">En retard</option>
              <option value="annule">Annulé</option>
            </select>

            {/* Filtre par type */}
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="flex-1 min-w-[140px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les types</option>
              <option value="scolarité">Scolarité</option>
              <option value="transport">Transport</option>
              <option value="inscription">Inscription</option>
              <option value="activité">Activité</option>
              <option value="autre">Autre</option>
            </select>

            {/* Filtre par année scolaire */}
            <select
              value={anneeFilter}
              onChange={(e) => setAnneeFilter(e.target.value)}
              className="flex-1 min-w-[160px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Toutes les années</option>
              {anneesScolaires.map((annee) => (
                <option key={annee.id_annee_scolaire} value={annee.libelle}>
                  {annee.libelle}
                </option>
              ))}
            </select>

            {/* Filtre par mois */}
            <select
              value={moisFilter}
              onChange={(e) => setMoisFilter(e.target.value)}
              className="flex-1 min-w-[120px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les mois</option>
              {moisOptions.map((mois) => (
                <option key={mois} value={mois}>
                  {mois}
                </option>
              ))}
            </select>

            {/* Filtre par classe */}
            <select
              value={classeFilter}
              onChange={(e) => setClasseFilter(e.target.value)}
              className="flex-1 min-w-[120px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Toutes les classes</option>
              {classes.map((classe) => (
                <option key={classe.id_classe} value={classe.nom_classe}>
                  {classe.nom_classe}
                </option>
              ))}
            </select>

            {/* Bouton Réinitialiser */}
            <Button
              icon={<RotateCcw size={14} />}
              variant="outline"
              size="sm"
              onClick={handleResetFilters}
            >
              Réinitialiser
            </Button>
          </div>
        </div>
      )}

      {/* Tableau des paiements */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Élève
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Période
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Montant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date paiement
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedPaiements.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    {filteredPaiements.length === 0 ? 'Aucun paiement trouvé' : 'Aucun paiement sur cette page'}
                  </td>
                </tr>
              ) : (
                paginatedPaiements.map((paiement) => (
                  <tr key={paiement.id_paiement} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {paiement.eleve?.prenom} {paiement.eleve?.nom}
                        </div>
                        <div className="text-sm text-gray-500">
                          {paiement.eleve?.classe}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(paiement.type_paiement)}`}>
                        {paiement.type_paiement}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {paiement.mois} {getAnneeFromPaiement(paiement)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatMontant(paiement.montant)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(paiement.statut)}`}>
                        {paiement.statut}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(paiement.date_paiement)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEdit(paiement)}
                          className="text-primary hover:text-secondary"
                          title="Modifier"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(paiement)}
                          className="text-red-600 hover:text-red-800"
                          title="Supprimer"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {filteredPaiements.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              showItemsPerPage={true}
            />
          </div>
        )}
      </div>

      {/* Modal de formulaire */}
      <Modal
        isOpen={showModal}
        onClose={handleModalClose}
        title={paiementToEdit ? "Modifier le paiement" : "Ajouter un paiement"}
      >
        <PaiementForm
          paiement={paiementToEdit}
          onSubmit={handlePaiementSubmit}
          onCancel={handleModalClose}
        />
      </Modal>

      {/* Modal de génération automatique */}
      <Modal
        isOpen={showGenerationModal}
        onClose={() => setShowGenerationModal(false)}
        title="Génération automatique des paiements mensuels"
      >
        <GenerationPaiementsMensuels
          onGenerated={handleGenerationSubmit}
          onClose={() => setShowGenerationModal(false)}
        />
      </Modal>

      {/* Composant de confirmation de suppression */}
      <ConfirmationComponent />
    </div>
  );
};

export default Paiements;
