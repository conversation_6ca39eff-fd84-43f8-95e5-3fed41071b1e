import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { login } from "../services/api";
import { useAuth } from "../context/AuthContext";
import Input from "../components/Input";
import Button from "../components/Button";
import {User,Lock} from "lucide-react";

export default function Login() {

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState({email:"", password:"", error:""});
  const navigate = useNavigate();
  const { setUser } = useAuth();

  const validerEmail = (email: string) => {
    const regex = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/;
    return regex.test(email);
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const newMessages = { email: "", password: "", role: "", error: "" };
    let hasError = false;

    if (!email) {
      newMessages.email = "Veuillez saisir votre adresse e-mail.";
      hasError = true;
    }else if (!validerEmail(email)) {
      newMessages.email = "Adresse e-mail invalide.";
      hasError = true;
    }
    if(!password){
      newMessages.password = "Veuillez saisir votre mot de passe.";
      hasError = true;
    }
    if (hasError) {
      setMessage(newMessages);
      return;
    }

    try {
      const response = await login({ email, password });
      console.log("Login API Response Data:", response.data);

    
      if (response.data.success && response.data.token) {
        localStorage.setItem("token", response.data.token);
        localStorage.setItem("user", JSON.stringify(response.data.user));
        setUser(response.data.user); // Mettre à jour le contexte avec l'utilisateur
        setMessage({ ...newMessages, error: "" });

        // Vérifier si l'utilisateur doit changer son mot de passe
        if (response.data.must_change_password) {
          navigate("/change-password");
          return;
        }

        // Redirection normale selon le rôle
        const role : string = response.data.user.role;
        if (role === "admin") {
          navigate("/admin/dashboard");
        } else if (role === "enseignant") {
          navigate("/enseignant/dashboard");
        } else if (role === "eleve") {
          navigate("/eleve/dashboard");
        } else if (role === "parent") {
          navigate("/parent/dashboard");
        } else {
          navigate("/");
        }
      }
    } catch (err: any) {
      console.error("API Error:", err.response?.data || err.message);
      setMessage({
        ...newMessages,
        error: err.response?.data?.message || "Erreur de connexion au serveur.",
      });
    }
    
    };
  

  return (
    <div className="flex items-center justify-center min-h-screen bg-background px-4">
      {/* <div>
        <div className="flex items-center justify-start px-7 py-4 text-primary">
          <img src="../../public/logo.svg" width={70} height={70} alt="Logo" />
          <span className="hidden lg:block text-2xl font-bold ">ScolaNova</span>
        </div>
        <p className="text-gray-700 mb-4">Système de gestion scolaire</p>
      </div> */}
      <div className="bg-white shadow-lg rounded-2xl p-8 w-full max-w-md">
        <h2 className="text-2xl font-bold text-secondary mb-6 text-center">Connexion</h2>

        {message.error && (
          <div className="mb-4 p-3 text-sm text-error">
            {message.error}
          </div>
        )}

        <form onSubmit={handleLogin} className="space-y-4">
        
          <Input
            type="text"
            id="email"
            label="Email"
            className="p-1"
            value={email}
            leftIcon = {<User size={18} />}
            onChange={(e) => setEmail(e.target.value)}
            error = {message.email}
            />

          <Input
            type="password"
            id="password"
            label="Mot de passe"
            className="p-1"
            value={password}
            leftIcon = {<Lock size={18} />}
            onChange={(e) => setPassword(e.target.value)}
            error = {message.password}
          />

          <Button size="md" fullWidth={true}>
            Se connecter
          </Button>
        </form>

        {/* <div className="text-center mt-4">
          <span className="text-sm text-gray-600">Pas encore de compte ? </span>
          <Link
            to = "/register"
            className="font-medium text-primary hover:text-secondary"
          >
            S’inscrire
          </Link>
        </div> */}

        <div className="text-center mt-4">
          <Link
            to="/"
            className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            ← Retour à l'accueil
          </Link>
        </div>

      </div>
    </div>
  );
};   
