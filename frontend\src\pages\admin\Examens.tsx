import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye, Users, Calendar, Clock, BookOpen } from 'lucide-react';
import Button from '../../components/Button';
import { getExamens, deleteExamen, getMatieres, getClasses, getEnseignants } from '../../services/api';
import { ExamenSimple, Matiere, Classe, Enseignant } from '../../types';
import { useDeleteConfirmation } from '../../hooks/useConfirmation';
import ExamenForm from '../../components/ExamenForm';
import NotesModal from '../../components/NotesModal';
import Pagination from '../../components/Pagination';
import { usePagination } from '../../hooks/usePagination';

const Examens: React.FC = () => {
  const [examens, setExamens] = useState<ExamenSimple[]>([]);
  const [filteredExamens, setFilteredExamens] = useState<ExamenSimple[]>([]);
  const [matieres, setMatieres] = useState<Matiere[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [enseignants, setEnseignants] = useState<Enseignant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [selectedExamen, setSelectedExamen] = useState<ExamenSimple | null>(null);

  // Filtres
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [matiereFilter, setMatiereFilter] = useState<string>('');
  const [classeFilter, setClasseFilter] = useState<string>('');
  const [semestreFilter, setSemestreFilter] = useState<string>('');

  const { confirmDelete, ConfirmationComponent } = useDeleteConfirmation();

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    filterExamens();
  }, [examens, searchTerm, typeFilter, matiereFilter, classeFilter, semestreFilter]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [examensRes, matieresRes, classesRes, enseignantsRes] = await Promise.all([
        getExamens(),
        getMatieres(),
        getClasses(),
        getEnseignants()
      ]);

      setExamens(examensRes.data.data || []);
      setMatieres(matieresRes.data.data || []);
      setClasses(classesRes.data.data || []);
      setEnseignants(enseignantsRes.data.data || []);
    } catch (error: any) {
      console.error('Erreur lors du chargement des données:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const filterExamens = () => {
    let filtered = examens;

    // Recherche textuelle
    if (searchTerm) {
      filtered = filtered.filter(examen =>
        examen.matiere?.nom_matiere_fr?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        examen.classe?.nom_classe?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        examen.enseignant?.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        examen.enseignant?.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        examen.commentaire?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par type
    if (typeFilter) {
      filtered = filtered.filter(examen => examen.type_examen === typeFilter);
    }

    // Filtre par matière
    if (matiereFilter) {
      filtered = filtered.filter(examen => examen.id_matiere.toString() === matiereFilter);
    }

    // Filtre par classe
    if (classeFilter) {
      filtered = filtered.filter(examen => examen.id_classe.toString() === classeFilter);
    }

    // Filtre par semestre
    if (semestreFilter) {
      filtered = filtered.filter(examen => examen.semestre === semestreFilter);
    }

    setFilteredExamens(filtered);
    resetPagination(); // Réinitialiser la pagination lors du filtrage
  };

  // Pagination
  const {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    paginatedData: paginatedExamens,
    setCurrentPage,
    setItemsPerPage,
    resetPagination
  } = usePagination({
    data: filteredExamens,
    initialItemsPerPage: 12
  });

  const handleAdd = () => {
    setSelectedExamen(null);
    setShowForm(true);
  };

  const handleEdit = (examen: ExamenSimple) => {
    setSelectedExamen(examen);
    setShowForm(true);
  };

  const handleDeleteClick = (id: number) => {
    confirmDelete(
      () => handleDelete(id),
      'cet examen',
      'Êtes-vous sûr de vouloir supprimer cet examen ? Cette action est irréversible et supprimera également toutes les notes associées.'
    );
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteExamen(id);
      await fetchData();
    } catch (error: any) {
      console.error('Erreur lors de la suppression:', error);
      setError('Erreur lors de la suppression de l\'examen');
    }
  };

  const handleViewNotes = (examen: ExamenSimple) => {
    setSelectedExamen(examen);
    setShowNotesModal(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setSelectedExamen(null);
    fetchData();
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setTypeFilter('');
    setMatiereFilter('');
    setClasseFilter('');
    setSemestreFilter('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatDuree = (minutes: number) => {
    const heures = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (heures > 0) {
      return mins > 0 ? `${heures}h${mins}` : `${heures}h`;
    }
    return `${mins}min`;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'examen': return 'bg-red-100 text-red-800';
      case 'devoir': return 'bg-blue-100 text-blue-800';
      case 'contrôle': return 'bg-orange-100 text-orange-800';
      case 'participation': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      

          
          <div className="relative">
            <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un examen..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          <div className="flex items-center gap-3">

            <Button
              icon={<Filter size={16} />}
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              Filtres
            </Button>

            <Button
              icon={<Plus size={16} />}
              variant="primary"
              onClick={handleAdd}
            >
              Nouvel examen
            </Button>
          </div>
      </div>

      {/* Panneau de filtres */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex flex-wrap items-center gap-3 justify-start">
            {/* Filtre par type */}
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="flex-1 min-w-[140px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les types</option>
              <option value="examen">Examen</option>
              <option value="devoir">Devoir</option>
              <option value="contrôle">Contrôle</option>
              <option value="participation">Participation</option>
            </select>

            {/* Filtre par matière */}
            <select
              value={matiereFilter}
              onChange={(e) => setMatiereFilter(e.target.value)}
              className="flex-1 min-w-[140px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Toutes les matières</option>
              {matieres.map((matiere) => (
                <option key={matiere.id_matiere} value={matiere.id_matiere}>
                  {matiere.nom_matiere_fr}
                </option>
              ))}
            </select>

            {/* Filtre par classe */}
            <select
              value={classeFilter}
              onChange={(e) => setClasseFilter(e.target.value)}
              className="flex-1 min-w-[120px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Toutes les classes</option>
              {classes.map((classe) => (
                <option key={classe.id_classe} value={classe.id_classe}>
                  {classe.nom_classe}
                </option>
              ))}
            </select>

            {/* Filtre par semestre */}
            <select
              value={semestreFilter}
              onChange={(e) => setSemestreFilter(e.target.value)}
              className="flex-1 min-w-[120px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">Tous les semestres</option>
              <option value="S1">Semestre 1</option>
              <option value="S2">Semestre 2</option>
            </select>

            {/* Bouton Réinitialiser */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetFilters}
            >
              Réinitialiser
            </Button>
          </div>
        </div>
      )}

      {/* Message d'erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Tableau des examens */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {/* <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Liste des examens ({totalItems})
          </h3>
        </div> */}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Matière
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Classe
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Durée
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Semestre
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Enseignant
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedExamens.map((examen) => (
                <tr key={examen.id_examen} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(examen.type_examen)}`}>
                      {examen.type_examen}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <BookOpen className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm font-medium text-gray-900">
                        {examen.matiere?.nom_matiere_fr}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {examen.classe?.nom_classe}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {formatDate(examen.date_examen)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {formatDuree(examen.duree_examen)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{examen.semestre}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">
                      {examen.enseignant ? `${examen.enseignant.prenom} ${examen.enseignant.nom}` : '-'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleViewNotes(examen)}
                        className="text-blue-600 hover:text-blue-900"
                        title="Voir les notes"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEdit(examen)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(examen.id_examen)}
                        className="text-red-600 hover:text-red-900"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {paginatedExamens.length === 0 && filteredExamens.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun examen</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || typeFilter || matiereFilter || classeFilter || semestreFilter
                  ? "Aucun examen ne correspond aux critères de recherche."
                  : "Commencez par créer un nouvel examen."}
              </p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {filteredExamens.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
              showItemsPerPage={true}
            />
          </div>
        )}
      </div>

      {/* Modals */}
      {showForm && (
        <ExamenForm
          examen={selectedExamen}
          onClose={() => setShowForm(false)}
          onSuccess={handleFormSuccess}
        />
      )}

      {showNotesModal && selectedExamen && (
        <NotesModal
          examen={selectedExamen}
          onClose={() => setShowNotesModal(false)}
        />
      )}

      <ConfirmationComponent />
    </div>
  );
};

export default Examens;
