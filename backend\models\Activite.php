<?php

class Activite {
    private $pdo;
    private $table = "Activite";

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Récupérer toutes les activités
    public function getAll() {
        try {
            $query = "
               SELECT 
        a.*,
        u.nom as nom_responsable,
        u.prenom as prenom_responsable,
        COUNT(DISTINCT ia.id_eleve) as nombre_inscrits,
        COALESCE(a.capacite_max, 0) - COUNT(DISTINCT ia.id_eleve) as places_restantes
    FROM {$this->table} a
    LEFT JOIN Enseignant e ON a.id_responsable = e.id_enseignant
    LEFT JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
    LEFT JOIN Inscription_Activite ia ON a.id_activite = ia.id_activite
    GROUP BY a.id_activite
    ORDER BY a.nom_activite
            ";
            error_log("⚠️ REQUÊTE ACTIVITÉS: " . $query);

            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer une activité par ID
    public function getById($id) {
        try {
            $query = "
                SELECT 
                    a.*,
                    u.nom as nom_responsable,
                    u.prenom as prenom_responsable,
                    COUNT(DISTINCT ia.id_eleve) as nombre_inscrits
                FROM {$this->table} a
                LEFT JOIN Enseignant e ON a.id_responsable = e.id_enseignant
                LEFT JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                LEFT JOIN Inscription_Activite ia ON a.id_activite = ia.id_activite
                WHERE a.id_activite = ?
                GROUP BY a.id_activite
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    // Créer une nouvelle activité
    public function create($data) {
        try {
            $query = "
                INSERT INTO {$this->table} (
                    nom_activite, description, type_activite, id_responsable,
                    jour_semaine, heure_debut, heure_fin, lieu, capacite_max,
                    prix, statut, date_debut, date_fin
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->pdo->prepare($query);
            $result = $stmt->execute([
                $data['nom_activite'],
                $data['description'],
                $data['type_activite'],
                $data['id_responsable'] ?? null,
                $data['jour_semaine'],
                $data['heure_debut'],
                $data['heure_fin'],
                $data['lieu'],
                $data['capacite_max'],
                $data['prix'],
                $data['statut'] ?? 'active',
                $data['date_debut'],
                $data['date_fin']
            ]);
            
            return $result ? $this->pdo->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Erreur dans create: " . $e->getMessage());
            return false;
        }
    }

    // Mettre à jour une activité
    public function update($id, $data) {
        try {
            $query = "
                UPDATE {$this->table} SET 
                    nom_activite = ?, description = ?, type_activite = ?,
                    id_responsable = ?, jour_semaine = ?, heure_debut = ?,
                    heure_fin = ?, lieu = ?, capacite_max = ?, prix = ?,
                    statut = ?, date_debut = ?, date_fin = ?
                WHERE id_activite = ?
            ";
            
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([
                $data['nom_activite'],
                $data['description'],
                $data['type_activite'],
                $data['id_responsable'] ?? null,
                $data['jour_semaine'],
                $data['heure_debut'],
                $data['heure_fin'],
                $data['lieu'],
                $data['capacite_max'],
                $data['prix'],
                $data['statut'],
                $data['date_debut'],
                $data['date_fin'],
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans update: " . $e->getMessage());
            return false;
        }
    }

    // Supprimer une activité
    public function delete($id) {
        try {
            // Vérifier s'il y a des inscriptions
            $checkQuery = "SELECT COUNT(*) FROM Inscription_Activite WHERE id_activite = ?";
            $checkStmt = $this->pdo->prepare($checkQuery);
            $checkStmt->execute([$id]);
            $count = $checkStmt->fetchColumn();

            if ($count > 0) {
                return ['error' => 'Impossible de supprimer cette activité car des élèves y sont inscrits'];
            }

            $stmt = $this->pdo->prepare("DELETE FROM {$this->table} WHERE id_activite = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les élèves inscrits à une activité
    public function getElevesInscrits($id_activite) {
        try {
            $query = "
                SELECT 
                    e.id_eleve,
                    u.nom,
                    u.prenom,
                    e.code_massar,
                    c.nom_classe,
                    ia.date_inscription,
                    ia.statut_inscription
                FROM Inscription_Activite ia
                JOIN Eleve e ON ia.id_eleve = e.id_eleve
                JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN Inscription i ON e.id_eleve = i.id_eleve
                JOIN Classe c ON i.id_classe = c.id_classe
                JOIN Annee_Scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                WHERE ia.id_activite = ? AND a.est_active = true
                ORDER BY u.nom, u.prenom
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_activite]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getElevesInscrits: " . $e->getMessage());
            return false;
        }
    }

    // Inscrire un élève à une activité
    public function inscrireEleve($id_activite, $id_eleve) {
        try {
            // Vérifier la capacité
            $activite = $this->getById($id_activite);
            if (!$activite) {
                return ['error' => 'Activité non trouvée'];
            }

            if ($activite['nombre_inscrits'] >= $activite['capacite_max']) {
                return ['error' => 'Activité complète'];
            }

            // Vérifier si déjà inscrit
            $checkQuery = "SELECT COUNT(*) FROM Inscription_Activite WHERE id_activite = ? AND id_eleve = ?";
            $checkStmt = $this->pdo->prepare($checkQuery);
            $checkStmt->execute([$id_activite, $id_eleve]);
            
            if ($checkStmt->fetchColumn() > 0) {
                return ['error' => 'Élève déjà inscrit à cette activité'];
            }

            $query = "
                INSERT INTO Inscription_Activite (id_activite, id_eleve, date_inscription, statut_inscription)
                VALUES (?, ?, NOW(), 'active')
            ";
            
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id_activite, $id_eleve]);
        } catch (PDOException $e) {
            error_log("Erreur dans inscrireEleve: " . $e->getMessage());
            return false;
        }
    }

    // Désinscrire un élève d'une activité
    public function desinscrireEleve($id_activite, $id_eleve) {
        try {
            $query = "DELETE FROM Inscription_Activite WHERE id_activite = ? AND id_eleve = ?";
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id_activite, $id_eleve]);
        } catch (PDOException $e) {
            error_log("Erreur dans desinscrireEleve: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les activités d'un élève
    public function getActivitesEleve($id_eleve) {
        try {
            $query = "
                SELECT 
                    a.*,
                    u.nom as nom_responsable,
                    u.prenom as prenom_responsable,
                    ia.date_inscription,
                    ia.statut_inscription
                FROM Inscription_Activite ia
                JOIN {$this->table} a ON ia.id_activite = a.id_activite
                LEFT JOIN Enseignant e ON a.id_responsable = e.id_enseignant
                LEFT JOIN Utilisateur u ON e.id_utilisateur = u.id_utilisateur
                WHERE ia.id_eleve = ? AND ia.statut_inscription = 'active'
                ORDER BY a.jour_semaine, a.heure_debut
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_eleve]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getActivitesEleve: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les statistiques des activités
    public function getStatistiques() {
        try {
            $stats = [];

            // Total activités
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM {$this->table} WHERE statut = 'active'");
            $stats['total_activites'] = $stmt->fetch()['total'];

            // Total inscriptions
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM Inscription_Activite WHERE statut_inscription = 'active'");
            $stats['total_inscriptions'] = $stmt->fetch()['total'];

            // Activité la plus populaire
            $query = "
                SELECT a.nom_activite, COUNT(ia.id_eleve) as nb_inscrits
                FROM {$this->table} a
                LEFT JOIN Inscription_Activite ia ON a.id_activite = ia.id_activite
                WHERE a.statut = 'active' AND (ia.statut_inscription = 'active' OR ia.statut_inscription IS NULL)
                GROUP BY a.id_activite
                ORDER BY nb_inscrits DESC
                LIMIT 1
            ";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $populaire = $stmt->fetch();
            $stats['activite_populaire'] = $populaire ? $populaire['nom_activite'] : 'Aucune';

            // Taux d'occupation moyen
            $query = "
                SELECT 
                    AVG((COUNT(ia.id_eleve) * 100.0) / a.capacite_max) as taux_occupation
                FROM {$this->table} a
                LEFT JOIN Inscription_Activite ia ON a.id_activite = ia.id_activite AND ia.statut_inscription = 'active'
                WHERE a.statut = 'active'
                GROUP BY a.id_activite
            ";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $taux = $stmt->fetch();
            $stats['taux_occupation'] = $taux ? round($taux['taux_occupation'], 1) : 0;

            return $stats;
        } catch (PDOException $e) {
            error_log("Erreur dans getStatistiques: " . $e->getMessage());
            return false;
        }
    }
}
?>
