<?php

class Activite {
    private $pdo;
    private $table = "activite";

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Récupérer toutes les activités
    public function getAll() {
        try {
            $query = "
                SELECT
                    a.*,
                    ans.libelle as annee_scolaire,
                    COUNT(DISTINCT pa.id_eleve) as nombre_inscrits
                FROM {$this->table} a
                LEFT JOIN annee_scolaire ans ON a.id_annee_scolaire = ans.id_annee_scolaire
                LEFT JOIN participation_activite pa ON a.id_activite = pa.id_activite
                GROUP BY a.id_activite
                ORDER BY a.nom_activite
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Ajouter des champs par défaut pour la compatibilité avec le frontend
            foreach ($results as &$result) {
                $result['id_responsable'] = null;
                $result['nom_responsable'] = null;
                $result['prenom_responsable'] = null;
                $result['jour_semaine'] = null;
                $result['heure_debut'] = null;
                $result['heure_fin'] = null;
                $result['lieu'] = null;
                $result['capacite_max'] = 50; // Valeur par défaut
                $result['statut'] = 'active';
                $result['places_restantes'] = $result['capacite_max'] - $result['nombre_inscrits'];
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer une activité par ID
    public function getById($id) {
        try {
            $query = "
                SELECT
                    a.*,
                    ans.libelle as annee_scolaire,
                    COUNT(DISTINCT pa.id_eleve) as nombre_inscrits
                FROM {$this->table} a
                LEFT JOIN annee_scolaire ans ON a.id_annee_scolaire = ans.id_annee_scolaire
                LEFT JOIN participation_activite pa ON a.id_activite = pa.id_activite
                WHERE a.id_activite = ?
                GROUP BY a.id_activite
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                // Ajouter des champs par défaut pour la compatibilité avec le frontend
                $result['id_responsable'] = null;
                $result['nom_responsable'] = null;
                $result['prenom_responsable'] = null;
                $result['jour_semaine'] = null;
                $result['heure_debut'] = null;
                $result['heure_fin'] = null;
                $result['lieu'] = null;
                $result['capacite_max'] = 50; // Valeur par défaut
                $result['statut'] = 'active';
                $result['places_restantes'] = $result['capacite_max'] - $result['nombre_inscrits'];
            }

            return $result;
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    // Créer une nouvelle activité
    public function create($data) {
        try {
            // Récupérer l'année scolaire active si non spécifiée
            $id_annee_scolaire = $data['id_annee_scolaire'] ?? null;
            if (!$id_annee_scolaire) {
                $stmt = $this->pdo->prepare("SELECT id_annee_scolaire FROM annee_scolaire WHERE est_active = true LIMIT 1");
                $stmt->execute();
                $annee = $stmt->fetch(PDO::FETCH_ASSOC);
                $id_annee_scolaire = $annee ? $annee['id_annee_scolaire'] : 1;
            }

            $query = "
                INSERT INTO {$this->table} (
                    id_annee_scolaire, nom_activite, type_activite,
                    date_debut, date_fin, prix, description
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ";

            $stmt = $this->pdo->prepare($query);
            $result = $stmt->execute([
                $id_annee_scolaire,
                $data['nom_activite'],
                $data['type_activite'],
                $data['date_debut'],
                $data['date_fin'],
                $data['prix'],
                $data['description'] ?? null
            ]);
            
            return $result ? $this->pdo->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Erreur dans create: " . $e->getMessage());
            return false;
        }
    }

    // Mettre à jour une activité
    public function update($id, $data) {
        try {
            $query = "
                UPDATE {$this->table} SET
                    nom_activite = ?, type_activite = ?, date_debut = ?,
                    date_fin = ?, prix = ?, description = ?
                WHERE id_activite = ?
            ";

            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([
                $data['nom_activite'],
                $data['type_activite'],
                $data['date_debut'],
                $data['date_fin'],
                $data['prix'],
                $data['description'] ?? null,
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans update: " . $e->getMessage());
            return false;
        }
    }

    // Supprimer une activité
    public function delete($id) {
        try {
            // Vérifier s'il y a des inscriptions
            $checkQuery = "SELECT COUNT(*) FROM Inscription_Activite WHERE id_activite = ?";
            $checkStmt = $this->pdo->prepare($checkQuery);
            $checkStmt->execute([$id]);
            $count = $checkStmt->fetchColumn();

            if ($count > 0) {
                return ['error' => 'Impossible de supprimer cette activité car des élèves y sont inscrits'];
            }

            $stmt = $this->pdo->prepare("DELETE FROM {$this->table} WHERE id_activite = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les élèves inscrits à une activité
    public function getElevesInscrits($id_activite) {
        try {
            $query = "
                SELECT
                    e.id_eleve,
                    u.nom,
                    u.prenom,
                    e.code_massar,
                    c.nom_classe,
                    NOW() as date_inscription,
                    'active' as statut_inscription
                FROM participation_activite pa
                JOIN eleve e ON pa.id_eleve = e.id_eleve
                JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN inscription i ON e.id_eleve = i.id_eleve
                JOIN classe c ON i.id_classe = c.id_classe
                JOIN annee_scolaire a ON i.id_annee_scolaire = a.id_annee_scolaire
                WHERE pa.id_activite = ? AND a.est_active = true
                ORDER BY u.nom, u.prenom
            ";

            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_activite]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getElevesInscrits: " . $e->getMessage());
            return false;
        }
    }

    // Inscrire un élève à une activité
    public function inscrireEleve($id_activite, $id_eleve) {
        try {
            // Vérifier la capacité
            $activite = $this->getById($id_activite);
            if (!$activite) {
                return ['error' => 'Activité non trouvée'];
            }

            if ($activite['nombre_inscrits'] >= $activite['capacite_max']) {
                return ['error' => 'Activité complète'];
            }

            // Vérifier si déjà inscrit
            $checkQuery = "SELECT COUNT(*) FROM participation_activite WHERE id_activite = ? AND id_eleve = ?";
            $checkStmt = $this->pdo->prepare($checkQuery);
            $checkStmt->execute([$id_activite, $id_eleve]);

            if ($checkStmt->fetchColumn() > 0) {
                return ['error' => 'Élève déjà inscrit à cette activité'];
            }

            $query = "
                INSERT INTO participation_activite (id_activite, id_eleve)
                VALUES (?, ?)
            ";

            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id_activite, $id_eleve]);
        } catch (PDOException $e) {
            error_log("Erreur dans inscrireEleve: " . $e->getMessage());
            return false;
        }
    }

    // Désinscrire un élève d'une activité
    public function desinscrireEleve($id_activite, $id_eleve) {
        try {
            $query = "DELETE FROM participation_activite WHERE id_activite = ? AND id_eleve = ?";
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id_activite, $id_eleve]);
        } catch (PDOException $e) {
            error_log("Erreur dans desinscrireEleve: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les activités d'un élève
    public function getActivitesEleve($id_eleve) {
        try {
            $query = "
                SELECT
                    a.*,
                    ans.libelle as annee_scolaire,
                    NOW() as date_inscription,
                    'active' as statut_inscription
                FROM participation_activite pa
                JOIN {$this->table} a ON pa.id_activite = a.id_activite
                LEFT JOIN annee_scolaire ans ON a.id_annee_scolaire = ans.id_annee_scolaire
                WHERE pa.id_eleve = ?
                ORDER BY a.nom_activite
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id_eleve]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getActivitesEleve: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les statistiques des activités
    public function getStatistiques() {
        try {
            $stats = [];

            // Total activités
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM {$this->table} WHERE statut = 'active'");
            $stats['total_activites'] = $stmt->fetch()['total'];

            // Total inscriptions
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM Inscription_Activite WHERE statut_inscription = 'active'");
            $stats['total_inscriptions'] = $stmt->fetch()['total'];

            // Activité la plus populaire
            $query = "
                SELECT a.nom_activite, COUNT(ia.id_eleve) as nb_inscrits
                FROM {$this->table} a
                LEFT JOIN Inscription_Activite ia ON a.id_activite = ia.id_activite
                WHERE a.statut = 'active' AND (ia.statut_inscription = 'active' OR ia.statut_inscription IS NULL)
                GROUP BY a.id_activite
                ORDER BY nb_inscrits DESC
                LIMIT 1
            ";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $populaire = $stmt->fetch();
            $stats['activite_populaire'] = $populaire ? $populaire['nom_activite'] : 'Aucune';

            // Taux d'occupation moyen
            $query = "
                SELECT 
                    AVG((COUNT(ia.id_eleve) * 100.0) / a.capacite_max) as taux_occupation
                FROM {$this->table} a
                LEFT JOIN Inscription_Activite ia ON a.id_activite = ia.id_activite AND ia.statut_inscription = 'active'
                WHERE a.statut = 'active'
                GROUP BY a.id_activite
            ";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $taux = $stmt->fetch();
            $stats['taux_occupation'] = $taux ? round($taux['taux_occupation'], 1) : 0;

            return $stats;
        } catch (PDOException $e) {
            error_log("Erreur dans getStatistiques: " . $e->getMessage());
            return false;
        }
    }
}
?>
