# 📊 Guide d'Implémentation du StandardTable

## 🎯 **Objectif**
Standardiser tous les tableaux de l'application avec un composant réutilisable qui inclut :
- Design cohérent
- Pagination intégrée
- Actions standardisées
- Gestion du loading et des états vides

## 🔧 **Composant StandardTable**

### **Fonctionnalités**
- ✅ **Design uniforme** : Style cohérent sur toute l'application
- ✅ **Pagination intégrée** : Plus besoin de gérer séparément
- ✅ **Actions standardisées** : Boutons Voir/Modifier/Supprimer uniformes
- ✅ **États gérés** : Loading, vide, erreur
- ✅ **Responsive** : Adapté mobile et desktop
- ✅ **Accessibilité** : Titres, couleurs, navigation

### **Props Principales**
```typescript
interface StandardTableProps {
  title: string;                    // Titre du tableau
  columns: Column[];                // Configuration des colonnes
  data: any[];                     // Données à afficher
  actions?: Action[];              // Actions par ligne
  loading?: boolean;               // État de chargement
  emptyMessage?: string;           // Message si vide
  emptyIcon?: React.ReactNode;     // Icône si vide
  // Pagination
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  itemsPerPage?: number;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showPagination?: boolean;
}
```

## 📋 **Configuration des Colonnes**

### **Structure d'une Colonne**
```typescript
interface Column {
  key: string;                     // Clé de la donnée
  label: string;                   // Titre de la colonne
  sortable?: boolean;              // Tri activé (futur)
  width?: string;                  // Largeur fixe
  align?: 'left' | 'center' | 'right';  // Alignement
  render?: (value: any, row: any) => React.ReactNode;  // Rendu personnalisé
}
```

### **Exemples de Colonnes**

#### **Colonne Simple**
```typescript
{
  key: "nom",
  label: "Nom",
  render: (value: string) => (
    <span className="font-medium text-gray-900">{value}</span>
  )
}
```

#### **Colonne avec Icône**
```typescript
{
  key: "enseignant",
  label: "Enseignant",
  render: (_: any, row: Enseignant) => (
    <div className="flex items-center">
      <User className="w-8 h-8 text-gray-400 mr-3" />
      <div>
        <div className="font-medium text-gray-900">
          {row.user?.prenom} {row.user?.nom}
        </div>
        <div className="text-sm text-gray-500">{row.num_CIN}</div>
      </div>
    </div>
  )
}
```

#### **Colonne avec Badge**
```typescript
{
  key: "statut",
  label: "Statut",
  render: (_: any, row: any) => (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
      row.statut === 'actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
    }`}>
      {row.statut === 'actif' ? 'Actif' : 'Inactif'}
    </span>
  )
}
```

## ⚡ **Configuration des Actions**

### **Structure d'une Action**
```typescript
interface Action {
  label: string;                   // Titre de l'action
  icon: React.ReactNode;           // Icône de l'action
  onClick: (row: any) => void;     // Fonction à exécuter
  color?: 'blue' | 'indigo' | 'red' | 'green' | 'yellow';  // Couleur
  show?: (row: any) => boolean;    // Condition d'affichage
}
```

### **Actions Standards**
```typescript
const tableActions = [
  {
    label: "Voir",
    icon: <Eye size={16} />,
    onClick: (item: any) => handleView(item),
    color: "blue" as const
  },
  {
    label: "Modifier",
    icon: <Edit size={16} />,
    onClick: (item: any) => handleEdit(item),
    color: "indigo" as const
  },
  {
    label: "Supprimer",
    icon: <Trash2 size={16} />,
    onClick: (item: any) => handleDelete(item.id, item.nom),
    color: "red" as const
  }
];
```

## 🎨 **Exemple Complet : Page Enseignants**

### **1. Imports**
```typescript
import StandardTable from "../../components/StandardTable";
import { User, Eye, Edit, Trash2, Users } from "lucide-react";
import { usePagination } from "../../hooks/usePagination";
```

### **2. Configuration des Colonnes**
```typescript
const standardColumns = [
  {
    key: "nom_complet",
    label: "Enseignant",
    render: (_: any, row: Enseignant) => (
      <div className="flex items-center">
        <User className="w-8 h-8 text-gray-400 mr-3" />
        <div>
          <div className="font-medium text-gray-900">
            {row.user?.prenom} {row.user?.nom}
          </div>
          <div className="text-sm text-gray-500">{row.num_CIN}</div>
        </div>
      </div>
    )
  },
  {
    key: "contact",
    label: "Contact",
    render: (_: any, row: Enseignant) => (
      <div>
        <div className="text-sm text-gray-900">{row.user?.telephone || "Non renseigné"}</div>
        <div className="text-sm text-gray-500">{row.user?.email || "Non renseigné"}</div>
      </div>
    )
  },
  // ... autres colonnes
];
```

### **3. Configuration des Actions**
```typescript
const tableActions = [
  {
    label: "Voir",
    icon: <Eye size={16} />,
    onClick: (enseignant: Enseignant) => handleView(enseignant),
    color: "blue" as const
  },
  {
    label: "Modifier",
    icon: <Edit size={16} />,
    onClick: (enseignant: Enseignant) => handleEdit(enseignant),
    color: "indigo" as const
  },
  {
    label: "Supprimer",
    icon: <Trash2 size={16} />,
    onClick: (enseignant: Enseignant) => handleDelete(enseignant.id_enseignant, `${enseignant.user?.prenom} ${enseignant.user?.nom}`),
    color: "red" as const
  }
];
```

### **4. Utilisation du Composant**
```typescript
<StandardTable
  title="Liste des enseignants"
  columns={standardColumns}
  data={paginatedEnseignants}
  actions={tableActions}
  loading={loading}
  emptyMessage="Aucun enseignant ne correspond à votre recherche."
  emptyIcon={<Users />}
  currentPage={currentPage}
  totalPages={totalPages}
  totalItems={totalItems}
  itemsPerPage={itemsPerPage}
  onPageChange={setCurrentPage}
  onItemsPerPageChange={setItemsPerPage}
  showPagination={true}
/>
```

## 🔄 **Migration des Pages Existantes**

### **Étapes de Migration**

#### **1. Remplacer les Imports**
```typescript
// Avant
import Table from "../../components/Table";
import Pagination from "../../components/Pagination";

// Après
import StandardTable from "../../components/StandardTable";
```

#### **2. Convertir les Colonnes**
```typescript
// Avant
const columns = [
  { header: "Nom", accessor: "nom", className: "font-medium" }
];

// Après
const standardColumns = [
  {
    key: "nom",
    label: "Nom",
    render: (value: string) => (
      <span className="font-medium text-gray-900">{value}</span>
    )
  }
];
```

#### **3. Remplacer le Tableau**
```typescript
// Avant
<Table columns={columns} data={data} renderRow={renderRow} />
<Pagination ... />

// Après
<StandardTable
  title="Liste des éléments"
  columns={standardColumns}
  data={paginatedData}
  actions={tableActions}
  // ... autres props
/>
```

## 📊 **Pages à Migrer**

### **✅ Terminé**
- [x] **Enseignants** : Migré vers StandardTable

### **🔄 En Cours**
- [ ] **Parents** : Import ajouté, colonnes à configurer
- [ ] **Classes** : À migrer
- [ ] **Salles** : À migrer
- [ ] **Matières** : À migrer

### **⏳ À Faire**
- [ ] **Activités** : Tableau personnalisé à adapter
- [ ] **Transport** : Tableau personnalisé à adapter
- [ ] **Examens** : Tableau personnalisé à adapter
- [ ] **Bulletins** : Tableau spécialisé à adapter

## 🎯 **Avantages du StandardTable**

### **Cohérence**
- ✅ **Design uniforme** sur toute l'application
- ✅ **Actions standardisées** avec couleurs cohérentes
- ✅ **Pagination intégrée** automatiquement

### **Maintenance**
- ✅ **Code centralisé** : Modifications dans un seul endroit
- ✅ **Réutilisabilité** : Même composant partout
- ✅ **Évolutivité** : Nouvelles fonctionnalités ajoutées automatiquement

### **Performance**
- ✅ **Optimisé** : Rendu efficace des grandes listes
- ✅ **Pagination intégrée** : Gestion automatique
- ✅ **États gérés** : Loading et vide optimisés

## 🚀 **Prochaines Étapes**

1. **Terminer la migration** des pages restantes
2. **Ajouter le tri** aux colonnes (sortable)
3. **Améliorer l'accessibilité** (ARIA labels)
4. **Ajouter des filtres** intégrés au tableau
5. **Optimiser les performances** avec virtualisation

Le StandardTable est maintenant prêt à être utilisé dans toute l'application ! 🎉
