<?php
// models/AbsenceEleve.php

class AbsenceEleve {
    private $pdo;
    private $table = "absence_eleve";

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Récupérer toutes les absences d'élèves avec filtres
    public function getAll($filters = []) {
        try {
            $query = "
                SELECT 
                    ae.*,
                    u.nom,
                    u.prenom,
                    e.code_massar,
                    c.nom_classe
                FROM {$this->table} ae
                JOIN eleve e ON ae.id_eleve = e.id_eleve
                JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN classe c ON ae.id_classe = c.id_classe
                WHERE 1=1
            ";
            
            $params = [];
            
            // Filtres
            if (!empty($filters['date_debut'])) {
                $query .= " AND DATE(ae.date_debut) >= ?";
                $params[] = $filters['date_debut'];
            }
            
            if (!empty($filters['date_fin'])) {
                $query .= " AND DATE(ae.date_fin) <= ?";
                $params[] = $filters['date_fin'];
            }
            
            if (!empty($filters['id_classe'])) {
                $query .= " AND ae.id_classe = ?";
                $params[] = $filters['id_classe'];
            }
            
            if (!empty($filters['motif'])) {
                $query .= " AND ae.motif = ?";
                $params[] = $filters['motif'];
            }
            
            if (isset($filters['justifiee'])) {
                $query .= " AND ae.justifiee = ?";
                $params[] = $filters['justifiee'] ? 1 : 0;
            }
            
            $query .= " ORDER BY ae.date_debut DESC";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getAll: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer une absence par ID
    public function getById($id) {
        try {
            $query = "
                SELECT 
                    ae.*,
                    u.nom,
                    u.prenom,
                    e.code_massar,
                    c.nom_classe
                FROM {$this->table} ae
                JOIN eleve e ON ae.id_eleve = e.id_eleve
                JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
                JOIN classe c ON ae.id_classe = c.id_classe
                WHERE ae.id_absence_eleve = ?
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getById: " . $e->getMessage());
            return false;
        }
    }

    // Créer une nouvelle absence
    public function create($data) {
        try {
            $query = "
                INSERT INTO {$this->table} (id_eleve, id_classe, date_debut, date_fin, motif, justifiee, commentaire)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->pdo->prepare($query);
            $result = $stmt->execute([
                $data['id_eleve'],
                $data['id_classe'],
                $data['date_debut'],
                $data['date_fin'],
                $data['motif'],
                $data['justifiee'] ? 1 : 0,
                $data['commentaire'] ?? null
            ]);
            
            if ($result) {
                return $this->pdo->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Erreur dans create: " . $e->getMessage());
            return false;
        }
    }

    // Mettre à jour une absence
    public function update($id, $data) {
        try {
            $query = "
                UPDATE {$this->table} SET 
                    id_eleve = ?, id_classe = ?, date_debut = ?, date_fin = ?, 
                    motif = ?, justifiee = ?, commentaire = ?
                WHERE id_absence_eleve = ?
            ";
            
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([
                $data['id_eleve'],
                $data['id_classe'],
                $data['date_debut'],
                $data['date_fin'],
                $data['motif'],
                $data['justifiee'] ? 1 : 0,
                $data['commentaire'] ?? null,
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Erreur dans update: " . $e->getMessage());
            return false;
        }
    }

    // Supprimer une absence
    public function delete($id) {
        try {
            $query = "DELETE FROM {$this->table} WHERE id_absence_eleve = ?";
            $stmt = $this->pdo->prepare($query);
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Erreur dans delete: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les absences d'un élève
    public function getByEleve($id_eleve, $date_debut = null, $date_fin = null) {
        try {
            $query = "
                SELECT ae.*, c.nom_classe
                FROM {$this->table} ae
                JOIN classe c ON ae.id_classe = c.id_classe
                WHERE ae.id_eleve = ?
            ";
            
            $params = [$id_eleve];
            
            if ($date_debut) {
                $query .= " AND DATE(ae.date_debut) >= ?";
                $params[] = $date_debut;
            }
            
            if ($date_fin) {
                $query .= " AND DATE(ae.date_fin) <= ?";
                $params[] = $date_fin;
            }
            
            $query .= " ORDER BY ae.date_debut DESC";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getByEleve: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les absences d'une classe
    public function getByClasse($id_classe, $date_debut = null, $date_fin = null) {
        try {
            $query = "
                SELECT 
                    ae.*,
                    u.nom,
                    u.prenom,
                    e.code_massar
                FROM {$this->table} ae
                JOIN eleve e ON ae.id_eleve = e.id_eleve
                JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
                WHERE ae.id_classe = ?
            ";
            
            $params = [$id_classe];
            
            if ($date_debut) {
                $query .= " AND DATE(ae.date_debut) >= ?";
                $params[] = $date_debut;
            }
            
            if ($date_fin) {
                $query .= " AND DATE(ae.date_fin) <= ?";
                $params[] = $date_fin;
            }
            
            $query .= " ORDER BY ae.date_debut DESC";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getByClasse: " . $e->getMessage());
            return false;
        }
    }

    // Récupérer les statistiques des absences
    public function getStatistiques($date_debut = null, $date_fin = null, $id_classe = null) {
        try {
            $whereClause = "WHERE 1=1";
            $params = [];
            
            if ($date_debut) {
                $whereClause .= " AND DATE(ae.date_debut) >= ?";
                $params[] = $date_debut;
            }
            
            if ($date_fin) {
                $whereClause .= " AND DATE(ae.date_fin) <= ?";
                $params[] = $date_fin;
            }
            
            if ($id_classe) {
                $whereClause .= " AND ae.id_classe = ?";
                $params[] = $id_classe;
            }
            
            $query = "
                SELECT 
                    COUNT(*) as total_absences,
                    COUNT(DISTINCT ae.id_eleve) as eleves_absents,
                    SUM(CASE WHEN ae.justifiee = 1 THEN 1 ELSE 0 END) as absences_justifiees,
                    SUM(CASE WHEN ae.justifiee = 0 THEN 1 ELSE 0 END) as absences_non_justifiees,
                    AVG(ae.duree) as duree_moyenne,
                    SUM(ae.duree) as duree_totale
                FROM {$this->table} ae
                {$whereClause}
            ";
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Erreur dans getStatistiques: " . $e->getMessage());
            return false;
        }
    }

    // Vérifier si un élève a déjà une absence à une date donnée
    public function hasAbsenceAtDate($id_eleve, $date_debut, $date_fin, $exclude_id = null) {
        try {
            $query = "
                SELECT COUNT(*) 
                FROM {$this->table} 
                WHERE id_eleve = ? 
                AND (
                    (date_debut <= ? AND date_fin >= ?) OR
                    (date_debut <= ? AND date_fin >= ?) OR
                    (date_debut >= ? AND date_fin <= ?)
                )
            ";
            
            $params = [$id_eleve, $date_debut, $date_debut, $date_fin, $date_fin, $date_debut, $date_fin];
            
            if ($exclude_id) {
                $query .= " AND id_absence_eleve != ?";
                $params[] = $exclude_id;
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Erreur dans hasAbsenceAtDate: " . $e->getMessage());
            return false;
        }
    }
}
?>
