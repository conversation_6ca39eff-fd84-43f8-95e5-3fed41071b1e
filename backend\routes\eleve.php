<?php

require_once __DIR__ . '/../controllers/EleveController.php';

function handleEleveRoutes($uri, $method, $pdo) {
    $controller = new EleveController($pdo);

    if ($uri === '/eleves') {
        if ($method === 'GET') {
            $controller->getEleves();
            return true;
        } elseif ($method === 'POST') {
            $controller->addEleve();
            return true;
        }
    } elseif ($uri === '/eleves/transport' && $method === 'GET') {
        $controller->getElevesTransport();
        return true;
    } elseif (preg_match('#^/eleves/annee/(\d+)$#', $uri, $matches)) {
        $id_annee = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getEleves($id_annee);
            return true;
        }
    } elseif (preg_match('#^/eleves/(\d+)$#', $uri, $matches)) {
        $id = (int)$matches[1];
        if ($method === 'GET') {
            $controller->getEleve($id);
            return true;
        } elseif ($method === 'PUT') {
            $controller->updateEleve($id);
            return true;
        } elseif ($method === 'DELETE') {
            $controller->deleteEleve($id);
            return true;
        }
    }
    // Route pour récupérer les enfants du parent connecté
    if ($uri === '/eleves/mes-enfants' && $method === 'GET') {
        $controller->getElevesByParent();
        return true;
    }

    if (preg_match('#^/eleves/id-eleve-by-utilisateur/(\d+)$#', $uri, $matches) && $method === 'GET') {
        $controller->getIdEleveByUtilisateur((int)$matches[1]);
        return true;
    }

    // Route pour récupérer le niveau d'un élève
    if (preg_match('#^/eleves/(\d+)/niveau$#', $uri, $matches) && $method === 'GET') {
        $controller->getNiveauEleve((int)$matches[1]);
        return true;
    }



    error_log("Route non reconnue dans handleEleveRoutes : $uri");
    return false;
}
