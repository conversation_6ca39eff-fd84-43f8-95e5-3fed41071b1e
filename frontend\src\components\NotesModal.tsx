import React, { useState, useEffect } from 'react';
import { X, Save, Users, BookOpen, Calendar, Clock, User } from 'lucide-react';
import Button from './Button';
import { getNotesExamen, getClasseEleves, addNote, updateNote } from '../services/api';
import { ExamenSimple, NoteSimple, Eleve } from '../types';

interface NotesModalProps {
  examen: ExamenSimple;
  onClose: () => void;
}

interface EleveNote {
  id_eleve: number;
  nom: string;
  prenom: string;
  code_massar: string;
  note?: number;
  hasNote: boolean;
}

const NotesModal: React.FC<NotesModalProps> = ({ examen, onClose }) => {
  const [elevesNotes, setElevesNotes] = useState<EleveNote[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, [examen.id_examen]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les élèves de la classe
      const elevesRes = await getClasseEleves(examen.id_classe);
      const eleves: Eleve[] = elevesRes.data.data || [];

      // Récupérer les notes existantes pour cet examen
      const notesRes = await getNotesExamen(examen.id_examen);
      const notes: NoteSimple[] = notesRes.data.data || [];

      // Combiner les données
      const elevesAvecNotes: EleveNote[] = eleves.map(eleve => {
        const noteExistante = notes.find(note => note.id_eleve === eleve.id_eleve);

        // Essayer différentes sources pour nom et prénom
        const nom = eleve.user?.nom || (eleve as any).nom || '';
        const prenom = eleve.user?.prenom || (eleve as any).prenom || '';

        console.log('Élève data:', {
          id: eleve.id_eleve,
          nom,
          prenom,
          user: eleve.user,
          eleveComplet: eleve
        });

        return {
          id_eleve: eleve.id_eleve,
          nom,
          prenom,
          code_massar: eleve.code_massar || '',
          note: noteExistante?.note,
          hasNote: !!noteExistante
        };
      });

      // Trier par nom de famille puis prénom
      elevesAvecNotes.sort((a, b) => {
        const nomCompare = a.nom.localeCompare(b.nom);
        if (nomCompare !== 0) return nomCompare;
        return a.prenom.localeCompare(b.prenom);
      });

      setElevesNotes(elevesAvecNotes);
    } catch (error: any) {
      console.error('Erreur lors du chargement des données:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleNoteChange = (eleveId: number, note: string) => {
    setElevesNotes(prev => prev.map(eleve => {
      if (eleve.id_eleve === eleveId) {
        const noteValue = note === '' ? undefined : parseFloat(note);
        return {
          ...eleve,
          note: noteValue
          // On garde hasNote tel qu'il était initialement pour savoir si la note existait en base
        };
      }
      return eleve;
    }));
  };

  const validateNote = (note: number | undefined): boolean => {
    if (note === undefined) return true; // Note vide autorisée
    return note >= 0 && note <= 20;
  };

  const handleSaveNotes = async () => {
    try {
      setSaving(true);
      setError(null);

      // Valider toutes les notes
      const invalidNotes = elevesNotes.filter(eleve => 
        eleve.note !== undefined && !validateNote(eleve.note)
      );

      if (invalidNotes.length > 0) {
        setError('Certaines notes sont invalides. Les notes doivent être entre 0 et 20.');
        return;
      }

      // Sauvegarder les notes modifiées
      const promises = elevesNotes.map(async (eleve) => {
        if (eleve.note !== undefined) {
          const noteData = {
            id_eleve: eleve.id_eleve,
            id_examen: examen.id_examen,
            note: eleve.note
          };

          try {
            // Essayer d'abord la mise à jour (plus probable si la note existe)
            return await updateNote(eleve.id_eleve, examen.id_examen, noteData);
          } catch (updateError: any) {
            // Si la mise à jour échoue (note n'existe pas), essayer la création
            if (updateError.response?.status === 404) {
              try {
                return await addNote(noteData);
              } catch (createError: any) {
                // Si la création échoue aussi (note existe déjà), réessayer la mise à jour
                if (createError.response?.status === 400 && createError.response?.data?.message?.includes('existe déjà')) {
                  return await updateNote(eleve.id_eleve, examen.id_examen, noteData);
                }
                throw createError;
              }
            }
            throw updateError;
          }
        }
      });

      await Promise.all(promises.filter(Boolean));

      setSuccessMessage('Notes sauvegardées avec succès');
      
      // Rafraîchir les données
      await fetchData();

      // Effacer le message de succès après 3 secondes
      setTimeout(() => setSuccessMessage(null), 3000);

    } catch (error: any) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError('Erreur lors de la sauvegarde des notes');
    } finally {
      setSaving(false);
    }
  };

  const formatDuree = (minutes: number) => {
    const heures = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (heures > 0) {
      return mins > 0 ? `${heures}h${mins}` : `${heures}h`;
    }
    return `${mins}min`;
  };

  const calculateStats = () => {
    const notesValides = elevesNotes
      .filter(eleve => eleve.note !== undefined)
      .map(eleve => eleve.note!);

    if (notesValides.length === 0) {
      return { moyenne: 0, min: 0, max: 0, count: 0 };
    }

    const moyenne = notesValides.reduce((sum, note) => sum + note, 0) / notesValides.length;
    const min = Math.min(...notesValides);
    const max = Math.max(...notesValides);

    return {
      moyenne: Math.round(moyenne * 100) / 100,
      min,
      max,
      count: notesValides.length
    };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg shadow-xl p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3">Chargement...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* En-tête */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Gestion des notes
            </h2>
            <div className="mt-2 space-y-1">
              <div className="flex items-center text-sm text-gray-600">
                <BookOpen className="w-4 h-4 mr-2" />
                <span>{examen.matiere?.nom_matiere_fr}</span>
                <span className="mx-2">•</span>
                <Users className="w-4 h-4 mr-1" />
                <span>{examen.classe?.nom_classe}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="w-4 h-4 mr-2" />
                <span>{new Date(examen.date_examen).toLocaleDateString('fr-FR')}</span>
                <span className="mx-2">•</span>
                <Clock className="w-4 h-4 mr-1" />
                <span>{formatDuree(examen.duree_examen)}</span>
                <span className="mx-2">•</span>
                <span className="capitalize">{examen.type_examen}</span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        {/* Messages */}
        {error && (
          <div className="mx-6 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="mx-6 mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {successMessage}
          </div>
        )}

        {/* Statistiques */}
        {stats.count > 0 && (
          <div className="mx-6 mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Statistiques</h3>
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-blue-700">Notes saisies:</span>
                <span className="ml-1 font-medium">{stats.count}/{elevesNotes.length}</span>
              </div>
              <div>
                <span className="text-blue-700">Moyenne:</span>
                <span className="ml-1 font-medium">{stats.moyenne}/20</span>
              </div>
              <div>
                <span className="text-blue-700">Note min:</span>
                <span className="ml-1 font-medium">{stats.min}/20</span>
              </div>
              <div>
                <span className="text-blue-700">Note max:</span>
                <span className="ml-1 font-medium">{stats.max}/20</span>
              </div>
            </div>
          </div>
        )}

        {/* Tableau des notes */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Élève
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Code Massar
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Note (/20)
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {elevesNotes.map((eleve) => (
                  <tr key={eleve.id_eleve} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {eleve.prenom} {eleve.nom}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {eleve.code_massar}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        min="0"
                        max="20"
                        step="0.25"
                        value={eleve.note !== undefined ? eleve.note : ''}
                        onChange={(e) => handleNoteChange(eleve.id_eleve, e.target.value)}
                        className={`w-20 px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 ${
                          eleve.note !== undefined && !validateNote(eleve.note)
                            ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                        }`}
                        placeholder="Note"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {elevesNotes.length === 0 && (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun élève</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Aucun élève trouvé dans cette classe.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Boutons */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={saving}
          >
            Fermer
          </Button>
          <Button
            variant="primary"
            onClick={handleSaveNotes}
            isLoading={saving}
            icon={<Save size={16} />}
          >
            Sauvegarder les notes
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotesModal;
