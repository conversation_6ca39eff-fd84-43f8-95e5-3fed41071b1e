-- Script d'insertion de données de test pour les transports
-- À exécuter après avoir créé la structure de base

-- Insertion de quelques transports de test
INSERT INTO transport (trajet, matricule, prix, capacite) VALUES
('Casablanca Centre - École ScolaNova', 'CAS-001-2024', 150.00, 25),
('Mohammedia - École ScolaNova', 'MOH-002-2024', 180.00, 30),
('<PERSON><PERSON> Agdal - École ScolaNova', 'RAB-003-2024', 200.00, 35),
('Salé - École ScolaNova', 'SAL-004-2024', 170.00, 28),
('Témara - École ScolaNova', 'TEM-005-2024', 160.00, 22);

-- Vérification des transports insérés
SELECT * FROM transport;

-- Exemple d'inscription d'élèves au transport (à adapter selon vos données d'élèves existantes)
-- Remplacez les ID par des ID d'élèves réels de votre base de données

-- Pour voir les élèves disponibles :
-- SELECT e.id_eleve, u.nom, u.prenom, e.code_massar 
-- FROM eleve e 
-- JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur 
-- LIMIT 10;

-- Exemple d'inscriptions (décommentez et adaptez les ID selon votre base) :
-- INSERT INTO beneficier_transport (id_eleve, id_transport, id_annee_scolaire) VALUES
-- (1, 1, 1),  -- Élève 1 inscrit au transport Casablanca pour l'année scolaire 1
-- (2, 1, 1),  -- Élève 2 inscrit au transport Casablanca pour l'année scolaire 1
-- (3, 2, 1),  -- Élève 3 inscrit au transport Mohammedia pour l'année scolaire 1
-- (4, 2, 1),  -- Élève 4 inscrit au transport Mohammedia pour l'année scolaire 1
-- (5, 3, 1);  -- Élève 5 inscrit au transport Rabat pour l'année scolaire 1

-- Requête pour vérifier les inscriptions avec détails :
-- SELECT 
--     t.trajet,
--     t.matricule,
--     u.nom,
--     u.prenom,
--     e.code_massar,
--     ans.libelle as annee_scolaire
-- FROM beneficier_transport bt
-- JOIN transport t ON bt.id_transport = t.id_transport
-- JOIN eleve e ON bt.id_eleve = e.id_eleve
-- JOIN utilisateur u ON e.id_utilisateur = u.id_utilisateur
-- JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire
-- ORDER BY t.trajet, u.nom;

-- Requête pour voir les statistiques des transports :
-- SELECT 
--     t.id_transport,
--     t.trajet,
--     t.matricule,
--     t.prix,
--     t.capacite,
--     COUNT(DISTINCT bt.id_eleve) as nombre_eleves_inscrits,
--     (t.capacite - COUNT(DISTINCT bt.id_eleve)) as places_disponibles,
--     ROUND((COUNT(DISTINCT bt.id_eleve) / t.capacite) * 100, 2) as taux_occupation
-- FROM transport t
-- LEFT JOIN beneficier_transport bt ON t.id_transport = bt.id_transport
-- LEFT JOIN annee_scolaire ans ON bt.id_annee_scolaire = ans.id_annee_scolaire AND ans.est_active = true
-- GROUP BY t.id_transport
-- ORDER BY t.trajet;
