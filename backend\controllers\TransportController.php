<?php
// controllers/TransportController.php
require_once __DIR__ . '/../models/Transport.php';
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../utils/auth.php';

class TransportController {
    private $transport;
    private $pdo;

    public function __construct() {
        $database = new Database();
        $this->pdo = $database->getConnection();
        $this->transport = new Transport($this->pdo);
    }

    // GET /transports - Récupérer tous les transports
    public function index() {
        try {
            $transports = $this->transport->getAll();
            
            if ($transports === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des transports'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $transports
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /transports/{id} - Récupérer un transport par ID
    public function show($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID transport invalide'
                ]);
                return;
            }

            $transport = $this->transport->getById($id);
            
            if (!$transport) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Transport non trouvé'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $transport
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // POST /transports - Créer un nouveau transport
    public function store() {
        try {
            $data = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            $errors = $this->validateTransportData($data);
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ]);
                return;
            }

            // Vérifier si le matricule existe déjà
            if ($this->transport->matriculeExists($data['matricule'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Ce matricule existe déjà'
                ]);
                return;
            }

            $result = $this->transport->create($data);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création du transport'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Transport créé avec succès',
                'data' => ['id' => $result]
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // PUT /transports/{id} - Mettre à jour un transport
    public function update($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID transport invalide'
                ]);
                return;
            }

            $data = json_decode(file_get_contents('php://input'), true);

            // Validation des données
            $errors = $this->validateTransportData($data);
            if (!empty($errors)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Données invalides',
                    'errors' => $errors
                ]);
                return;
            }

            // Vérifier si le matricule existe déjà (en excluant le transport actuel)
            if ($this->transport->matriculeExists($data['matricule'], $id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Ce matricule existe déjà'
                ]);
                return;
            }

            $result = $this->transport->update($id, $data);
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la mise à jour du transport'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Transport mis à jour avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // DELETE /transports/{id} - Supprimer un transport
    public function destroy($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID transport invalide'
                ]);
                return;
            }

            $result = $this->transport->delete($id);
            
            if (is_array($result) && isset($result['error'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['error']
                ]);
                return;
            }
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la suppression du transport'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Transport supprimé avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /transports/{id}/eleves - Récupérer les élèves inscrits à un transport
    public function getEleves($id) {
        try {
            if (!$id || !is_numeric($id)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID transport invalide'
                ]);
                return;
            }

            $eleves = $this->transport->getElevesInscrits($id);
            
            if ($eleves === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des élèves'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $eleves
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // POST /transports/{id}/eleves/{id_eleve} - Inscrire un élève à un transport
    public function inscrireEleve($id, $id_eleve) {
        try {
            if (!$id || !is_numeric($id) || !$id_eleve || !is_numeric($id_eleve)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'IDs invalides'
                ]);
                return;
            }

            $result = $this->transport->inscrireEleve($id, $id_eleve);
            
            if (is_array($result) && isset($result['error'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['error']
                ]);
                return;
            }
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de l\'inscription de l\'élève'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Élève inscrit avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // DELETE /transports/{id}/eleves/{id_eleve} - Désinscrire un élève d'un transport
    public function desinscrireEleve($id, $id_eleve) {
        try {
            if (!$id || !is_numeric($id) || !$id_eleve || !is_numeric($id_eleve)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'IDs invalides'
                ]);
                return;
            }

            $result = $this->transport->desinscrireEleve($id, $id_eleve);
            
            if (is_array($result) && isset($result['error'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $result['error']
                ]);
                return;
            }
            
            if ($result === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la désinscription de l\'élève'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'message' => 'Élève désinscrit avec succès'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // GET /transports/statistiques - Récupérer les statistiques des transports
    public function getStatistiques() {
        try {
            $stats = $this->transport->getStatistiques();
            
            if ($stats === false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la récupération des statistiques'
                ]);
                return;
            }

            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Erreur serveur',
                'error' => $e->getMessage()
            ]);
        }
    }

    // Validation des données de transport
    private function validateTransportData($data) {
        $errors = [];

        if (empty($data['trajet'])) {
            $errors[] = 'Le trajet est obligatoire';
        }

        if (empty($data['matricule'])) {
            $errors[] = 'Le matricule est obligatoire';
        }

        if (!isset($data['prix']) || !is_numeric($data['prix']) || $data['prix'] < 0) {
            $errors[] = 'Le prix doit être un nombre positif';
        }

        if (isset($data['capacite']) && (!is_numeric($data['capacite']) || $data['capacite'] < 1)) {
            $errors[] = 'La capacité doit être un nombre positif';
        }

        return $errors;
    }
}
?>
